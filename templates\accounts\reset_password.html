{% extends 'base.html' %}
{% load static %}

{% block title %}إعادة تعيين كلمة المرور | جريت كارت{% endblock %}

{% block content %}

<!-- ========================= محتوى إعادة تعيين كلمة المرور ========================= -->
<section class="section-content padding-y bg-light">
	<div class="container">
		<div class="row justify-content-center">
			<div class="col-md-6 col-lg-5">
				<!-- رسائل النجاح والخطأ -->
				{% if messages %}
				<div class="mb-4">
					{% for message in messages %}
					<div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
						<i class="fa fa-info-circle mr-2"></i>
						{{ message }}
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					{% endfor %}
				</div>
				{% endif %}

				<!-- بطاقة إعادة تعيين كلمة المرور -->
				<div class="card shadow-lg border-0">
					<div class="card-header bg-success text-white text-center">
						<h4 class="mb-0">
							<i class="fa fa-key mr-2"></i>
							إعادة تعيين كلمة المرور
						</h4>
						<p class="mb-0 mt-2">أدخل كلمة المرور الجديدة</p>
					</div>
					<div class="card-body p-4">
						<div class="text-center mb-4">
							<i class="fa fa-shield-alt fa-3x text-success mb-3"></i>
							<p class="text-muted">
								اختر كلمة مرور قوية وآمنة لحسابك
							</p>
						</div>

						<form method="post" class="reset-password-form">
							{% csrf_token %}
							
							<!-- كلمة المرور الجديدة -->
							<div class="form-group mb-4">
								<label for="new_password" class="form-label">
									<i class="fa fa-lock mr-2"></i>
									كلمة المرور الجديدة <span class="text-danger">*</span>
								</label>
								<div class="input-group">
									<input type="password" 
										   class="form-control" 
										   id="new_password" 
										   name="new_password" 
										   required
										   minlength="8"
										   placeholder="أدخل كلمة المرور الجديدة">
									<div class="input-group-append">
										<button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
											<i class="fa fa-eye" id="new_password_icon"></i>
										</button>
									</div>
								</div>
								<small class="form-text text-muted">
									8 أحرف على الأقل
								</small>
							</div>

							<!-- تأكيد كلمة المرور الجديدة -->
							<div class="form-group mb-4">
								<label for="confirm_password" class="form-label">
									<i class="fa fa-check-circle mr-2"></i>
									تأكيد كلمة المرور الجديدة <span class="text-danger">*</span>
								</label>
								<div class="input-group">
									<input type="password" 
										   class="form-control" 
										   id="confirm_password" 
										   name="confirm_password" 
										   required
										   minlength="8"
										   placeholder="أعد إدخال كلمة المرور الجديدة">
									<div class="input-group-append">
										<button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
											<i class="fa fa-eye" id="confirm_password_icon"></i>
										</button>
									</div>
								</div>
								<small class="form-text text-muted">
									يجب أن تطابق كلمة المرور الجديدة
								</small>
							</div>

							<!-- مؤشر قوة كلمة المرور -->
							<div class="form-group mb-4">
								<label class="form-label">قوة كلمة المرور:</label>
								<div class="progress" style="height: 8px;">
									<div class="progress-bar" id="password-strength" role="progressbar" style="width: 0%"></div>
								</div>
								<small class="form-text" id="password-strength-text">أدخل كلمة المرور لرؤية قوتها</small>
							</div>

							<!-- زر إعادة التعيين -->
							<div class="form-group mb-3">
								<button type="submit" class="btn btn-success btn-lg btn-block">
									<i class="fa fa-save mr-2"></i>
									حفظ كلمة المرور الجديدة
								</button>
							</div>

							<!-- رابط تسجيل الدخول -->
							<div class="text-center">
								<p class="mb-0">
									تم تعيين كلمة المرور؟ 
									<a href="{% url 'accounts:login' %}" class="text-primary font-weight-bold">
										سجل دخولك الآن
									</a>
								</p>
							</div>
						</form>
					</div>
				</div>

				<!-- نصائح الأمان -->
				<div class="card mt-4 border-0 bg-transparent">
					<div class="card-body text-center">
						<h6 class="text-muted mb-3">نصائح لكلمة مرور قوية</h6>
						<div class="row">
							<div class="col-md-6">
								<h6><i class="fa fa-check text-success mr-2"></i>استخدم:</h6>
								<ul class="list-unstyled text-right">
									<li><i class="fa fa-check-circle text-success mr-2"></i>8 أحرف على الأقل</li>
									<li><i class="fa fa-check-circle text-success mr-2"></i>أحرف كبيرة وصغيرة</li>
									<li><i class="fa fa-check-circle text-success mr-2"></i>أرقام ورموز</li>
									<li><i class="fa fa-check-circle text-success mr-2"></i>كلمة مرور فريدة</li>
								</ul>
							</div>
							<div class="col-md-6">
								<h6><i class="fa fa-times text-danger mr-2"></i>تجنب:</h6>
								<ul class="list-unstyled text-right">
									<li><i class="fa fa-times-circle text-danger mr-2"></i>المعلومات الشخصية</li>
									<li><i class="fa fa-times-circle text-danger mr-2"></i>كلمات المرور الشائعة</li>
									<li><i class="fa fa-times-circle text-danger mr-2"></i>التواريخ المهمة</li>
									<li><i class="fa fa-times-circle text-danger mr-2"></i>أسماء الأشخاص</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
<!-- ========================= نهاية محتوى إعادة تعيين كلمة المرور ========================= -->

<script>
// إظهار/إخفاء كلمة المرور
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// فحص قوة كلمة المرور
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.getElementById('password-strength');
    const strengthText = document.getElementById('password-strength-text');
    
    let strength = 0;
    let feedback = [];
    
    // طول كلمة المرور
    if (password.length >= 8) {
        strength += 20;
    } else {
        feedback.push('8 أحرف على الأقل');
    }
    
    // أحرف صغيرة
    if (/[a-z]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('أحرف صغيرة');
    }
    
    // أحرف كبيرة
    if (/[A-Z]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('أحرف كبيرة');
    }
    
    // أرقام
    if (/[0-9]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('أرقام');
    }
    
    // رموز خاصة
    if (/[^A-Za-z0-9]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('رموز خاصة');
    }
    
    // تحديث شريط القوة
    strengthBar.style.width = strength + '%';
    
    if (strength < 40) {
        strengthBar.className = 'progress-bar bg-danger';
        strengthText.textContent = 'ضعيفة - تحتاج: ' + feedback.join(', ');
        strengthText.className = 'form-text text-danger';
    } else if (strength < 80) {
        strengthBar.className = 'progress-bar bg-warning';
        strengthText.textContent = 'متوسطة - تحتاج: ' + feedback.join(', ');
        strengthText.className = 'form-text text-warning';
    } else {
        strengthBar.className = 'progress-bar bg-success';
        strengthText.textContent = 'قوية - ممتازة!';
        strengthText.className = 'form-text text-success';
    }
});

// التحقق من تطابق كلمات المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && newPassword !== confirmPassword) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
        if (confirmPassword) {
            this.classList.add('is-valid');
        }
    }
});
</script>

{% endblock %}
