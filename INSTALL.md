# دليل التثبيت السريع - جريت كارت

## التثبيت السريع

### 1. متطلبات النظام
- Python 3.11 أو أحدث
- pip (مدير حزم Python)
- Git (اختياري)

### 2. تحميل المشروع
```bash
# إذا كان لديك Git
git clone <repository-url>
cd django_greatkart_arabic

# أو قم بتحميل الملف المضغوط واستخراجه
```

### 3. إعداد البيئة الافتراضية
```bash
# إنشاء البيئة الافتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 4. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 5. <PERSON><PERSON><PERSON><PERSON> قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 6. إنشاء مستخدم إداري
```bash
python manage.py createsuperuser
```

### 7. تشغيل المشروع
```bash
python manage.py runserver
```

### 8. زيارة الموقع
افتح المتصفح واذهب إلى: http://127.0.0.1:8000/

## الروابط المهمة
- الصفحة الرئيسية: http://127.0.0.1:8000/
- المتجر: http://127.0.0.1:8000/store/
- لوحة الإدارة: http://127.0.0.1:8000/admin/

## استكشاف الأخطاء

### مشكلة: خطأ في تثبيت Pillow
```bash
# Windows
pip install Pillow --upgrade

# Linux
sudo apt-get install python3-dev python3-setuptools
pip install Pillow
```

### مشكلة: خطأ في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm db.sqlite3
python manage.py makemigrations
python manage.py migrate
```

### مشكلة: الملفات الثابتة لا تظهر
```bash
python manage.py collectstatic
```

## للمطورين

### تثبيت أدوات التطوير
```bash
pip install -r requirements-dev.txt
```

### تشغيل الاختبارات
```bash
python manage.py test
```

### فحص جودة الكود
```bash
flake8 .
black .
isort .
```
