from django.urls import path
from . import views

app_name = 'accounts'

urlpatterns = [
    path('register/', views.register, name='register'),
    path('login/', views.login, name='login'),
    path('logout/', views.logout, name='logout'),
    path('dashboard/', views.dashboard, name='dashboard'),
    path('', views.dashboard, name='dashboard'),
    path('my_orders/', views.my_orders, name='my_orders'),
    path('edit_profile/', views.edit_profile, name='edit_profile'),
    path('change_password/', views.change_password, name='change_password'),
    path('order_detail/<int:order_id>/', views.order_detail, name='order_detail'),

    # إعادة تعيين كلمة المرور
    path('forgot_password/', views.forgot_password, name='forgot_password'),
    path('forgot_password_sent/', views.forgot_password_sent, name='forgot_password_sent'),
    path('reset_password_validate/<uidb64>/<token>/', views.reset_password_validate, name='reset_password_validate'),
    path('reset_password/', views.reset_password, name='reset_password'),

    # اختبار إعدادات البريد الإلكتروني (للمطورين فقط)
    path('test_email/', views.test_email_settings, name='test_email'),
]