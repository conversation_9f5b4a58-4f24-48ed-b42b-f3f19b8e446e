# جريت كارت - متجر إلكتروني عربي

مشروع متجر إلكتروني متكامل باللغة العربية مبني بـ Django.

## المميزات

- 🛒 نظام سلة تسوق متكامل
- 👤 نظام إدارة المستخدمين والحسابات
- 📦 إدارة المنتجات والفئات
- 🎨 تصميم متجاوب يدعم اللغة العربية (RTL)
- 💳 نظام الدفع والطلبات
- 📧 نظام إرسال الإيميلات
- ⭐ نظام تقييم المنتجات
- 🔍 البحث والفلترة

## متطلبات النظام

- Python 3.11+
- Django 5.2.1
- SQLite (افتراضي) أو PostgreSQL

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd django_greatkart_arabic
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv
```

### 3. تفعيل البيئة الافتراضية
```bash
# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 4. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 5. إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 6. إنشاء مستخدم إداري
```bash
python manage.py createsuperuser
```

### 7. تشغيل الخادم
```bash
python manage.py runserver
```

الآن يمكنك زيارة الموقع على: http://127.0.0.1:8000/

## هيكل المشروع

```
django_greatkart_arabic/
├── greatkart/          # إعدادات المشروع الرئيسية
├── store/              # تطبيق المتجر والمنتجات
├── cart/               # تطبيق سلة التسوق
├── accounts/           # تطبيق إدارة المستخدمين
├── orders/             # تطبيق الطلبات
├── templates/          # قوالب HTML
├── static/             # ملفات CSS, JS, Images
├── media/              # ملفات المستخدمين المرفوعة
└── requirements.txt    # متطلبات المشروع
```

## للتطوير

لتثبيت أدوات التطوير:
```bash
pip install -r requirements-dev.txt
```

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
