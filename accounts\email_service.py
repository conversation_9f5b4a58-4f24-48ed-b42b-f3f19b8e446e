"""
خدمة إرسال البريد الإلكتروني البديلة
يمكن استخدام هذا الملف لإضافة خدمات بريد إلكتروني مختلفة
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

def send_reset_email_alternative(user_email, reset_url, user_name):
    """
    إرسال بريد إلكتروني بديل لإعادة تعيين كلمة المرور
    """
    try:
        # إعداد الرسالة
        msg = MIMEMultipart('alternative')
        msg['Subject'] = 'إعادة تعيين كلمة المرور - جريت كارت'
        msg['From'] = 'جريت كارت <<EMAIL>>'
        msg['To'] = user_email

        # محتوى الرسالة النصي
        text_content = f"""
مرحباً {user_name}،

تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك في جريت كارت.

يرجى الضغط على الرابط التالي لإعادة تعيين كلمة المرور:
{reset_url}

إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذه الرسالة.

هذا الرابط صالح لمدة 24 ساعة فقط.

شكراً لك،
فريق جريت كارت
        """

        # محتوى الرسالة HTML
        html_content = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <style>
        body {{ font-family: Arial, sans-serif; direction: rtl; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f9f9f9; }}
        .button {{ 
            display: inline-block; 
            background-color: #28a745; 
            color: white; 
            padding: 12px 24px; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 20px 0;
        }}
        .footer {{ padding: 20px; text-align: center; color: #666; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>إعادة تعيين كلمة المرور</h1>
            <p>جريت كارت - متجرك الإلكتروني المفضل</p>
        </div>
        
        <div class="content">
            <h2>مرحباً {user_name}،</h2>
            
            <p>تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك في جريت كارت.</p>
            
            <p>يرجى الضغط على الزر أدناه لإعادة تعيين كلمة المرور:</p>
            
            <div style="text-align: center;">
                <a href="{reset_url}" class="button">إعادة تعيين كلمة المرور</a>
            </div>
            
            <p>أو انسخ والصق الرابط التالي في متصفحك:</p>
            <p style="word-break: break-all; background-color: #e9ecef; padding: 10px;">{reset_url}</p>
            
            <div style="background-color: #fff3cd; padding: 15px; margin: 20px 0; border-radius: 5px;">
                <strong>ملاحظات مهمة:</strong>
                <ul>
                    <li>هذا الرابط صالح لمدة 24 ساعة فقط</li>
                    <li>لا تشارك هذا الرابط مع أي شخص آخر</li>
                    <li>إذا لم تطلب إعادة تعيين كلمة المرور، تجاهل هذه الرسالة</li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>فريق جريت كارت</strong></p>
            <p>هذه رسالة تلقائية، يرجى عدم الرد عليها</p>
        </div>
    </div>
</body>
</html>
        """

        # إضافة المحتوى للرسالة
        part1 = MIMEText(text_content, 'plain', 'utf-8')
        part2 = MIMEText(html_content, 'html', 'utf-8')
        
        msg.attach(part1)
        msg.attach(part2)

        # محاولة الإرسال باستخدام خدمات مختلفة
        success = False
        
        # محاولة 1: Gmail
        if not success:
            success = try_gmail_smtp(msg, user_email)
        
        # محاولة 2: Outlook/Hotmail
        if not success:
            success = try_outlook_smtp(msg, user_email)
        
        # محاولة 3: Yahoo
        if not success:
            success = try_yahoo_smtp(msg, user_email)
        
        return success

    except Exception as e:
        logger.error(f"خطأ في إرسال البريد الإلكتروني: {str(e)}")
        return False

def try_gmail_smtp(msg, user_email):
    """محاولة الإرسال عبر Gmail"""
    try:
        server = smtplib.SMTP('smtp.gmail.com', 587)
        server.starttls()
        # يمكن إضافة بيانات اعتماد Gmail هنا
        # server.login('<EMAIL>', 'your-app-password')
        # server.send_message(msg)
        server.quit()
        return False  # مؤقت<|im_start|> - حتى يتم إضافة بيانات صحيحة
    except Exception as e:
        logger.error(f"فشل إرسال Gmail: {str(e)}")
        return False

def try_outlook_smtp(msg, user_email):
    """محاولة الإرسال عبر Outlook"""
    try:
        server = smtplib.SMTP('smtp-mail.outlook.com', 587)
        server.starttls()
        # يمكن إضافة بيانات اعتماد Outlook هنا
        server.quit()
        return False  # مؤقت<|im_start|> - حتى يتم إضافة بيانات صحيحة
    except Exception as e:
        logger.error(f"فشل إرسال Outlook: {str(e)}")
        return False

def try_yahoo_smtp(msg, user_email):
    """محاولة الإرسال عبر Yahoo"""
    try:
        server = smtplib.SMTP('smtp.mail.yahoo.com', 587)
        server.starttls()
        # يمكن إضافة بيانات اعتماد Yahoo هنا
        server.quit()
        return False  # مؤقت<|im_start|> - حتى يتم إضافة بيانات صحيحة
    except Exception as e:
        logger.error(f"فشل إرسال Yahoo: {str(e)}")
        return False

def send_email_via_api(user_email, reset_url, user_name):
    """
    إرسال البريد الإلكتروني عبر API خدمات مثل SendGrid أو Mailgun
    """
    try:
        # يمكن إضافة خدمات API هنا
        # مثل SendGrid, Mailgun, Amazon SES, إلخ
        
        # مثال لـ SendGrid (يحتاج تثبيت sendgrid package):
        # import sendgrid
        # from sendgrid.helpers.mail import Mail
        # 
        # sg = sendgrid.SendGridAPIClient(api_key='your-api-key')
        # message = Mail(
        #     from_email='<EMAIL>',
        #     to_emails=user_email,
        #     subject='إعادة تعيين كلمة المرور - جريت كارت',
        #     html_content=html_content
        # )
        # response = sg.send(message)
        # return response.status_code == 202
        
        return False  # مؤقت<|im_start|> - حتى يتم تفعيل خدمة API
        
    except Exception as e:
        logger.error(f"فشل إرسال API: {str(e)}")
        return False
