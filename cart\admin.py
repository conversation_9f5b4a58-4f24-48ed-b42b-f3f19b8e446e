from django.contrib import admin
from .models import Cart, CartItem

class CartAdmin(admin.ModelAdmin):
    list_display = ('cart_id', 'date_added')
    search_fields = ['cart_id']
    list_per_page = 20
    fieldsets = (
        ('معلومات السلة', {
            'fields': ('cart_id', 'date_added')
        }),
    )

class CartItemAdmin(admin.ModelAdmin):
    list_display = ('product', 'cart', 'quantity', 'is_active', 'user')
    list_filter = ['is_active']
    search_fields = ['product__name', 'cart__cart_id', 'user__email']
    list_per_page = 20
    fieldsets = (
        ('معلومات العنصر', {
            'fields': ('product', 'cart', 'quantity', 'is_active')
        }),
        ('معلومات المستخدم', {
            'fields': ('user',)
        }),
    )

admin.site.register(Cart, CartAdmin)
admin.site.register(CartItem, CartItemAdmin)
