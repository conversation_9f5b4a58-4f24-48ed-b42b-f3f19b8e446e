<!-- Widget لعرض حالة الطلبات في الصفحة الرئيسية -->
{% if user.is_authenticated %}
<div class="card mb-4">
    <div class="card-header">
        <h6><i class="fas fa-bell"></i> آخر طلباتك</h6>
    </div>
    <div class="card-body">
        {% if recent_orders %}
            {% for order in recent_orders %}
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                <div>
                    <strong>طلب #{{ order.order_number }}</strong><br>
                    <small class="text-muted">{{ order.created_at|date:"d/m/Y" }}</small>
                </div>
                <div class="text-center">
                    <span class="badge badge-{{ order.get_status_color }}">{{ order.get_status_display }}</span><br>
                    <small>{{ order.order_total }} جنيه</small>
                </div>
                <div>
                    <a href="{% url 'orders:track_order' %}?order_number={{ order.order_number }}" 
                       class="btn btn-sm btn-outline-primary">تتبع</a>
                </div>
            </div>
            {% endfor %}
            <div class="text-center mt-3">
                <a href="{% url 'orders:my_orders' %}" class="btn btn-sm btn-primary">عرض جميع الطلبات</a>
            </div>
        {% else %}
            <div class="text-center">
                <i class="fas fa-shopping-bag text-muted" style="font-size: 2rem;"></i>
                <p class="mt-2 mb-0">لا توجد طلبات حتى الآن</p>
                <a href="{% url 'store:store' %}" class="btn btn-sm btn-primary mt-2">ابدأ التسوق</a>
            </div>
        {% endif %}
    </div>
</div>
{% endif %}
