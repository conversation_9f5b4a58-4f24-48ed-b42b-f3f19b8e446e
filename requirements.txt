# Django GreatKart Arabic E-commerce Project Requirements
# Python 3.11+ recommended

# Core Django Framework
Django==5.2.1

# Database and ORM
asgiref==3.8.1
sqlparse==0.5.3

# Image Processing (for product images)
Pillow==11.2.1

# Timezone Support
tzdata==2025.2

# Additional packages that might be needed for production:
# gunicorn==21.2.0  # WSGI HTTP Server for production
# psycopg2-binary==2.9.7  # PostgreSQL adapter (if using PostgreSQL)
# python-decouple==3.8  # For environment variables
# whitenoise==6.5.0  # Static files serving
# django-crispy-forms==2.0  # Better form rendering
# django-allauth==0.54.0  # Authentication system
# celery==5.3.1  # Task queue (for background tasks)
# redis==4.6.0  # Cache and message broker
# boto3==1.28.25  # AWS SDK (for S3 storage)
# django-storages==1.13.2  # Cloud storage backends

# Development packages (uncomment for development):
# django-debug-toolbar==4.1.0  # Debug toolbar
# django-extensions==3.2.3  # Additional management commands
