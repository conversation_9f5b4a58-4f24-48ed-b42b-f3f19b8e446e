# دليل إعداد البريد الإلكتروني - جريت كارت

## المشكلة المحلولة

تم إصلاح المشاكل التالية في نظام نسيت كلمة المرور:

1. ✅ **إزالة المشكلة الأمنية**: لم يعد يتم عرض رابط إعادة تعيين كلمة المرور مباشرة في الصفحة
2. ✅ **تحسين إعدادات البريد الإلكتروني**: إضافة مرونة في التحكم بإعدادات البريد
3. ✅ **تحسين معالجة الأخطاء**: رسائل خطأ أكثر وضوحاً وأماناً

## إعداد البريد الإلكتروني للإرسال الفعلي

### الخطوة 1: إنشاء ملف .env

انسخ ملف `.env.example` إلى `.env`:

```bash
cp .env.example .env
```

### الخطوة 2: إعداد Gmail

1. **إنشاء App Password لـ Gmail:**
   - اذهب إلى [Google Account Settings](https://myaccount.google.com/)
   - اختر "Security" → "2-Step Verification"
   - اختر "App passwords"
   - أنشئ كلمة مرور للتطبيق

2. **تحديث ملف .env:**
```env
EMAIL_BACKEND=smtp
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=جريت كارت <<EMAIL>>
```

### الخطوة 3: إعداد خدمات بريد أخرى

#### Outlook/Hotmail:
```env
EMAIL_HOST=smtp-mail.outlook.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-password
```

#### Yahoo:
```env
EMAIL_HOST=smtp.mail.yahoo.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
```

### الخطوة 4: أوضاع الاختبار

#### وضع الملف (افتراضي):
```env
EMAIL_BACKEND=file
```
سيتم حفظ البريد في مجلد `sent_emails`

#### وضع وحدة التحكم:
```env
EMAIL_BACKEND=console
```
سيتم عرض البريد في terminal

#### وضع الإرسال الفعلي:
```env
EMAIL_BACKEND=smtp
```

## اختبار النظام

### 1. اختبار نسيت كلمة المرور:

1. اذهب إلى `/accounts/forgot_password/`
2. أدخل بريد إلكتروني مسجل
3. تحقق من:
   - عدم ظهور رابط مباشر في الصفحة
   - وصول البريد الإلكتروني (حسب الوضع المختار)

### 2. التحقق من الأمان:

- ✅ لا يمكن الوصول لرابط إعادة التعيين بدون البريد الإلكتروني
- ✅ الرابط صالح لمدة 24 ساعة فقط
- ✅ الرابط يستخدم مرة واحدة فقط

## استكشاف الأخطاء

### مشكلة: البريد لا يُرسل

**الحلول:**
1. تأكد من صحة بيانات البريد الإلكتروني
2. تأكد من تفعيل 2FA وإنشاء App Password
3. تحقق من إعدادات الجدار الناري
4. جرب وضع console للاختبار

### مشكلة: خطأ في الاتصال

**الحلول:**
1. تحقق من إعدادات PORT و HOST
2. تأكد من اتصال الإنترنت
3. جرب خدمة بريد مختلفة

### مشكلة: البريد يذهب إلى Spam

**الحلول:**
1. استخدم بريد إلكتروني موثوق
2. أضف SPF و DKIM records
3. استخدم خدمة بريد متخصصة مثل SendGrid

## الأمان

### التحسينات المطبقة:

1. **عدم عرض الرابط مباشرة**: الرابط يُرسل فقط عبر البريد الإلكتروني
2. **Token آمن**: استخدام Django's default_token_generator
3. **انتهاء صلاحية**: الرابط صالح لمدة 24 ساعة
4. **استخدام واحد**: الرابط يُستخدم مرة واحدة فقط
5. **تسجيل الأخطاء**: تسجيل محاولات الوصول غير المصرح بها

### نصائح إضافية:

- استخدم HTTPS في الإنتاج
- فعّل rate limiting لمنع الإساءة
- راقب محاولات إعادة تعيين كلمة المرور
- استخدم خدمة بريد متخصصة في الإنتاج

## الدعم

إذا واجهت أي مشاكل:

1. تحقق من logs Django
2. تحقق من ملف `sent_emails` في وضع file
3. جرب وضع console للاختبار
4. تأكد من صحة إعدادات .env

---

**ملاحظة**: هذا النظام آمن ويتبع أفضل الممارسات لإعادة تعيين كلمة المرور.
