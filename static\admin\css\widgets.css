/* SELECTOR (FILTER INTERFACE) */

.selector {
    display: flex;
    flex: 1;
    gap: 0 10px;
}

.selector select {
    height: 17.2em;
    flex: 1 0 auto;
    overflow: scroll;
    width: 100%;
}

.selector-available, .selector-chosen {
    display: flex;
    flex-direction: column;
    flex: 1 1;
}

.selector-available-title, .selector-chosen-title {
    border: 1px solid var(--border-color);
    border-radius: 4px 4px 0 0;
}

.selector .helptext {
    font-size: 0.6875rem;
}

.selector-chosen .list-footer-display {
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 4px 4px;
    margin: 0 0 10px;
    padding: 8px;
    text-align: center;
    background: var(--primary);
    color: var(--header-link-color);
    cursor: pointer;
}
.selector-chosen .list-footer-display__clear {
    color: var(--breadcrumbs-fg);
}

.selector-chosen-title {
    background: var(--secondary);
    color: var(--header-link-color);
    padding: 8px;
}

.aligned .selector-chosen-title label {
    color: var(--header-link-color);
    width: 100%;
}

.selector-available-title {
    background: var(--darkened-bg);
    color: var(--body-quiet-color);
    padding: 8px;
}

.aligned .selector-available-title label {
    width: 100%;
}

.selector .selector-filter {
    border: 1px solid var(--border-color);
    border-width: 0 1px;
    padding: 8px;
    color: var(--body-quiet-color);
    font-size: 0.625rem;
    margin: 0;
    text-align: left;
    display: flex;
    gap: 8px;
}

.selector .selector-filter label,
.inline-group .aligned .selector .selector-filter label {
    float: left;
    margin: 7px 0 0;
    width: 18px;
    height: 18px;
    padding: 0;
    overflow: hidden;
    line-height: 1;
    min-width: auto;
}

.selector-filter input {
    flex-grow: 1;
}

.selector ul.selector-chooser {
    align-self: center;
    width: 30px;
    background-color: var(--selected-bg);
    border-radius: 10px;
    margin: 0;
    padding: 0;
    transform: translateY(-17px);
}

.selector-chooser li {
    margin: 0;
    padding: 3px;
    list-style-type: none;
}

.selector select {
    padding: 0 10px;
    margin: 0 0 10px;
    border-radius: 0 0 4px 4px;
}
.selector .selector-chosen--with-filtered select {
    margin: 0;
    border-radius: 0;
    height: 14em;
}

.selector .selector-chosen:not(.selector-chosen--with-filtered) .list-footer-display {
    display: none;
}

.selector-add, .selector-remove {
    width: 24px;
    height: 24px;
    display: block;
    text-indent: -3000px;
    overflow: hidden;
    cursor: default;
    opacity: 0.55;
    border: none;
}

:enabled.selector-add, :enabled.selector-remove {
    opacity: 1;
}

:enabled.selector-add:hover, :enabled.selector-remove:hover {
    cursor: pointer;
}

.selector-add {
    background: url(../img/selector-icons.svg) 0 -144px no-repeat;
    background-size: 24px auto;
}

:enabled.selector-add:focus, :enabled.selector-add:hover {
    background-position: 0 -168px;
}

.selector-remove {
    background: url(../img/selector-icons.svg) 0 -96px no-repeat;
    background-size: 24px auto;
}

:enabled.selector-remove:focus, :enabled.selector-remove:hover {
    background-position: 0 -120px;
}

.selector-chooseall, .selector-clearall {
    display: inline-block;
    height: 16px;
    text-align: left;
    margin: 0 auto;
    overflow: hidden;
    font-weight: bold;
    line-height: 16px;
    color: var(--body-quiet-color);
    text-decoration: none;
    opacity: 0.55;
    border: none;
}

:enabled.selector-chooseall:focus, :enabled.selector-clearall:focus,
:enabled.selector-chooseall:hover, :enabled.selector-clearall:hover {
    color: var(--link-fg);
}

:enabled.selector-chooseall, :enabled.selector-clearall {
    opacity: 1;
}

:enabled.selector-chooseall:hover, :enabled.selector-clearall:hover {
    cursor: pointer;
}

.selector-chooseall {
    padding: 0 18px 0 0;
    background: url(../img/selector-icons.svg) right -160px no-repeat;
    cursor: default;
}

:enabled.selector-chooseall:focus, :enabled.selector-chooseall:hover {
    background-position: 100% -176px;
}

.selector-clearall {
    padding: 0 0 0 18px;
    background: url(../img/selector-icons.svg) 0 -128px no-repeat;
    cursor: default;
}

:enabled.selector-clearall:focus, :enabled.selector-clearall:hover {
    background-position: 0 -144px;
}

/* STACKED SELECTORS */

.stacked {
    float: left;
    width: 490px;
    display: block;
}

.stacked select {
    width: 480px;
    height: 10.1em;
}

.stacked .selector-available, .stacked .selector-chosen {
    width: 480px;
}

.stacked .selector-available {
    margin-bottom: 0;
}

.stacked .selector-available input {
    width: 422px;
}

.stacked ul.selector-chooser {
    display: flex;
    height: 30px;
    width: 64px;
    margin: 0 0 10px 40%;
    background-color: #eee;
    border-radius: 10px;
    transform: none;
}

.stacked .selector-chooser li {
    float: left;
    padding: 3px 3px 3px 5px;
}

.stacked .selector-chooseall, .stacked .selector-clearall {
    display: none;
}

.stacked .selector-add {
    background: url(../img/selector-icons.svg) 0 -48px no-repeat;
    background-size: 24px auto;
    cursor: default;
}

.stacked :enabled.selector-add {
    background-position: 0 -48px;
    cursor: pointer;
}

.stacked :enabled.selector-add:focus, .stacked :enabled.selector-add:hover {
    background-position: 0 -72px;
    cursor: pointer;
}

.stacked .selector-remove {
    background: url(../img/selector-icons.svg) 0 0 no-repeat;
    background-size: 24px auto;
    cursor: default;
}

.stacked :enabled.selector-remove {
    background-position: 0 0px;
    cursor: pointer;
}

.stacked :enabled.selector-remove:focus, .stacked :enabled.selector-remove:hover {
    background-position: 0 -24px;
    cursor: pointer;
}

.selector .help-icon {
    background: url(../img/icon-unknown.svg) 0 0 no-repeat;
    display: inline-block;
    vertical-align: middle;
    margin: -2px 0 0 2px;
    width: 13px;
    height: 13px;
}

.selector .selector-chosen .help-icon {
    background: url(../img/icon-unknown-alt.svg) 0 0 no-repeat;
}

.selector .search-label-icon {
    background: url(../img/search.svg) 0 0 no-repeat;
    display: inline-block;
    height: 1.125rem;
    width: 1.125rem;
}

/* DATE AND TIME */

p.datetime {
    line-height: 20px;
    margin: 0;
    padding: 0;
    color: var(--body-quiet-color);
    font-weight: bold;
}

.datetime span {
    white-space: nowrap;
    font-weight: normal;
    font-size: 0.6875rem;
    color: var(--body-quiet-color);
}

.datetime input, .form-row .datetime input.vDateField, .form-row .datetime input.vTimeField {
    margin-left: 5px;
    margin-bottom: 4px;
}

table p.datetime {
    font-size: 0.6875rem;
    margin-left: 0;
    padding-left: 0;
}

.datetimeshortcuts .clock-icon, .datetimeshortcuts .date-icon {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    height: 24px;
    width: 24px;
    overflow: hidden;
}

.datetimeshortcuts .clock-icon {
    background: url(../img/icon-clock.svg) 0 0 no-repeat;
    background-size: 24px auto;
}

.datetimeshortcuts a:focus .clock-icon,
.datetimeshortcuts a:hover .clock-icon {
    background-position: 0 -24px;
}

.datetimeshortcuts .date-icon {
    background: url(../img/icon-calendar.svg) 0 0 no-repeat;
    background-size: 24px auto;
    top: -1px;
}

.datetimeshortcuts a:focus .date-icon,
.datetimeshortcuts a:hover .date-icon {
    background-position: 0 -24px;
}

.timezonewarning {
    font-size: 0.6875rem;
    color: var(--body-quiet-color);
}

/* URL */

p.url {
    line-height: 20px;
    margin: 0;
    padding: 0;
    color: var(--body-quiet-color);
    font-size: 0.6875rem;
    font-weight: bold;
}

.url a {
    font-weight: normal;
}

/* FILE UPLOADS */

p.file-upload {
    line-height: 20px;
    margin: 0;
    padding: 0;
    color: var(--body-quiet-color);
    font-size: 0.6875rem;
    font-weight: bold;
}

.file-upload a {
    font-weight: normal;
}

.file-upload .deletelink {
    margin-left: 5px;
}

span.clearable-file-input label {
    color: var(--body-fg);
    font-size: 0.6875rem;
    display: inline;
    float: none;
}

/* CALENDARS & CLOCKS */

.calendarbox, .clockbox {
    margin: 5px auto;
    font-size: 0.75rem;
    width: 19em;
    text-align: center;
    background: var(--body-bg);
    color: var(--body-fg);
    border: 1px solid var(--hairline-color);
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    position: relative;
}

.clockbox {
    width: auto;
}

.calendar {
    margin: 0;
    padding: 0;
}

.calendar table {
    margin: 0;
    padding: 0;
    border-collapse: collapse;
    background: white;
    width: 100%;
}

.calendar caption, .calendarbox h2 {
    margin: 0;
    text-align: center;
    border-top: none;
    font-weight: 700;
    font-size: 0.75rem;
    color: #333;
    background: var(--accent);
}

.calendar th {
    padding: 8px 5px;
    background: var(--darkened-bg);
    border-bottom: 1px solid var(--border-color);
    font-weight: 400;
    font-size: 0.75rem;
    text-align: center;
    color: var(--body-quiet-color);
}

.calendar td {
    font-weight: 400;
    font-size: 0.75rem;
    text-align: center;
    padding: 0;
    border-top: 1px solid var(--hairline-color);
    border-bottom: none;
}

.calendar td.selected a {
    background: var(--secondary);
    color: var(--button-fg);
}

.calendar td.nonday {
    background: var(--darkened-bg);
}

.calendar td.today a {
    font-weight: 700;
}

.calendar td a, .timelist a {
    display: block;
    font-weight: 400;
    padding: 6px;
    text-decoration: none;
    color: var(--body-quiet-color);
}

.calendar td a:focus, .timelist a:focus,
.calendar td a:hover, .timelist a:hover {
    background: var(--primary);
    color: white;
}

.calendar td a:active, .timelist a:active {
    background: var(--header-bg);
    color: white;
}

.calendarnav {
    font-size: 0.625rem;
    text-align: center;
    color: #ccc;
    margin: 0;
    padding: 1px 3px;
}

.calendarnav a:link, #calendarnav a:visited,
#calendarnav a:focus, #calendarnav a:hover {
    color: var(--body-quiet-color);
}

.calendar-shortcuts {
    background: var(--body-bg);
    color: var(--body-quiet-color);
    font-size: 0.6875rem;
    line-height: 0.6875rem;
    border-top: 1px solid var(--hairline-color);
    padding: 8px 0;
}

.calendarbox .calendarnav-previous, .calendarbox .calendarnav-next {
    display: block;
    position: absolute;
    top: 8px;
    width: 15px;
    height: 15px;
    text-indent: -9999px;
    padding: 0;
}

.calendarnav-previous {
    left: 10px;
    background: url(../img/calendar-icons.svg) 0 0 no-repeat;
}

.calendarnav-next {
    right: 10px;
    background: url(../img/calendar-icons.svg) 0 -15px no-repeat;
}

.calendar-cancel {
    margin: 0;
    padding: 4px 0;
    font-size: 0.75rem;
    background: var(--close-button-bg);
    border-top: 1px solid var(--border-color);
    color: var(--button-fg);
}

.calendar-cancel:focus, .calendar-cancel:hover {
    background: var(--close-button-hover-bg);
}

.calendar-cancel a {
    color: var(--button-fg);
    display: block;
}

ul.timelist, .timelist li {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

.timelist a {
    padding: 2px;
}

/* EDIT INLINE */

.inline-deletelink {
    float: right;
    text-indent: -9999px;
    background: url(../img/inline-delete.svg) 0 0 no-repeat;
    width: 1.5rem;
    height: 1.5rem;
    border: 0px none;
    margin-bottom: .25rem;
}

.inline-deletelink:focus, .inline-deletelink:hover {
    cursor: pointer;
}

/* RELATED WIDGET WRAPPER */
.related-widget-wrapper {
    display: flex;
    gap: 0 10px;
    flex-grow: 1;
    flex-wrap: wrap;
    margin-bottom: 5px;
}

.related-widget-wrapper-link {
    opacity: .6;
    filter: grayscale(1);
}

.related-widget-wrapper-link:link {
    opacity: 1;
    filter: grayscale(0);
}

/* GIS MAPS */
.dj_map {
    width: 600px;
    height: 400px;
}
