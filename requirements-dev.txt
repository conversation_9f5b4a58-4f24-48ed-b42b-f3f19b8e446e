# Development Requirements
# Install with: pip install -r requirements-dev.txt

# Include base requirements
-r requirements.txt

# Development and Testing Tools
django-debug-toolbar==4.2.0
django-extensions==3.2.3

# Code Quality and Formatting
flake8==6.0.0
black==23.7.0
isort==5.12.0

# Testing
pytest==7.4.0
pytest-django==4.5.2
coverage==7.2.7

# Documentation
Sphinx==7.1.2

# Environment Management
python-decouple==3.8
