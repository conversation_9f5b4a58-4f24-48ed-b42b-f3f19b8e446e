{% extends 'base.html' %}
{% load static %}

{% block title %}{{ single_product.name }} | جريت كارت{% endblock %}

{% block content %}
<section class="section-content padding-y bg">
<div class="container">
<!-- ============================ COMPONENT 1 ================================= -->
<div class="card">
	<div class="row no-gutters">
		<aside class="col-md-6">
			<article class="gallery-wrap">
				<div class="img-big-wrap">
					<a href="#">
						{% if single_product.image %}
							<img src="{{ single_product.image.url }}" alt="{{ single_product.name }}">
						{% else %}
							<img src="{% static 'images/default-product.png' %}" alt="{{ single_product.name }}">
						{% endif %}
					</a>
				</div>
			</article>
		</aside>
		<main class="col-md-6 border-right">
			<article class="content-body">
				<h2 class="title">{{ single_product.name }}</h2>

				<div class="mb-3">
					<var class="price h4">{{ single_product.price }} جنيه</var>
				</div>

				<p>{{ single_product.description }}</p>

				<hr>
				<div class="row">
					<div class="item-option-select">
						<h6>اختر الحجم</h6>
						<select class="form-control" name="size">
							<option value="small">صغير</option>
							<option value="medium">متوسط</option>
							<option value="large">كبير</option>
						</select>
					</div>
				</div>
				<hr>
				{% if single_product.stock <= 0 %}
					<h5 class="text-danger">غير متوفر</h5>
				{% else %}
					{% if in_cart %}
						<a href="{% url 'cart:cart' %}" class="btn  btn-success"> <span class="text">في السلة</span> <i class="fas fa-check"></i></a>
						<a href="{% url 'cart:cart' %}" class="btn  btn-outline-primary"> <span class="text">عرض السلة</span> <i class="fas fa-eye"></i></a>
					{% else %}
						<a href="{% url 'cart:add_cart' single_product.id %}" class="btn  btn-primary"> <span class="text">أضف للسلة</span> <i class="fas fa-shopping-cart"></i></a>
					{% endif %}
				{% endif %}
			</article>
		</main>
	</div>
</div>

<!-- ===========================  REVIEWS  ============================== -->
<div class="row">
	<div class="col-md-9">
		<header class="section-heading">
			<h3>تقييمات المنتج </h3>
		</header>
		{% for review in reviews %}
		<article class="box mb-3">
			<div class="icontext w-100">
				<div class="text">
					<span class="date text-muted float-md-left">{{ review.updated_at }}</span>
					<h6 class="mb-1">{{ review.user.full_name }}</h6>
					<div class="rating-star">
						<span>
							<i class="fa fa-star{% if review.rating == 0.5 %}-half-o{% elif review.rating < 1 %}-o {% endif %}" aria-hidden="true"></i>
							<i class="fa fa-star{% if review.rating == 1.5 %}-half-o{% elif review.rating < 2 %}-o {% endif %}" aria-hidden="true"></i>
							<i class="fa fa-star{% if review.rating == 2.5 %}-half-o{% elif review.rating < 3 %}-o {% endif %}" aria-hidden="true"></i>
							<i class="fa fa-star{% if review.rating == 3.5 %}-half-o{% elif review.rating < 4 %}-o {% endif %}" aria-hidden="true"></i>
							<i class="fa fa-star{% if review.rating == 4.5 %}-half-o{% elif review.rating < 5 %}-o {% endif %}" aria-hidden="true"></i>
						</span>
					</div>
				</div>
			</div>
			<div class="mt-3">
				<h6>{{ review.subject }}</h6>
				<p>{{ review.review }}</p>
			</div>
		</article>
		{% endfor %}

		{% if user.is_authenticated %}
			<form action="{% url 'store:submit_review' single_product.id %}" method="POST">
				{% csrf_token %}
				<h5>اكتب تقييمك</h5>
				<div class="form-group">
					<label>التقييم</label>
					<br>
					<div class="rate">
						<input type="radio" name="rating" id="rating10" value="5" required /><label for="rating10" title="ممتاز"></label>
						<input type="radio" name="rating" id="rating9" value="4.5" required /><label for="rating9" title="4.5 نجوم"></label>
						<input type="radio" name="rating" id="rating8" value="4" required /><label for="rating8" title="جيد جداً"></label>
						<input type="radio" name="rating" id="rating7" value="3.5" required /><label for="rating7" title="3.5 نجوم"></label>
						<input type="radio" name="rating" id="rating6" value="3" required /><label for="rating6" title="جيد"></label>
						<input type="radio" name="rating" id="rating5" value="2.5" required /><label for="rating5" title="2.5 نجوم"></label>
						<input type="radio" name="rating" id="rating4" value="2" required /><label for="rating4" title="مقبول"></label>
						<input type="radio" name="rating" id="rating3" value="1.5" required /><label for="rating3" title="1.5 نجوم"></label>
						<input type="radio" name="rating" id="rating2" value="1" required /><label for="rating2" title="سيئ"></label>
						<input type="radio" name="rating" id="rating1" value="0.5" required /><label for="rating1" title="سيئ جداً"></label>
					</div>
				</div>
				<div class="form-group">
					<label>عنوان التقييم</label>
					<input type="text" class="form-control" name="subject" required>
				</div>
				<div class="form-group">
					<label>التقييم</label>
					<textarea name="review" rows="4" class="form-control" required></textarea>
				</div>
				<button type="submit" class="btn btn-primary">إرسال</button>
			</form>
		{% else %}
			<p>الرجاء <a href="{% url 'accounts:login' %}">تسجيل الدخول</a> لكتابة تقييم.</p>
		{% endif %}
	</div>
</div>
<!-- ===========================  REVIEWS END  ============================== -->

</div>
</section>
{% endblock %}