from django.core.management.base import BaseCommand
from store.models import Category, Product
from django.utils.text import slugify

class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية للمتجر'

    def handle(self, *args, **options):
        self.stdout.write('🚀 بدء إنشاء البيانات التجريبية...')

        # إنشاء الفئات
        categories_data = [
            {'name': 'الإلكترونيات', 'description': 'أجهزة إلكترونية وتقنية متنوعة'},
            {'name': 'الملابس والأزياء', 'description': 'ملابس رجالية ونسائية عصرية'},
            {'name': 'الكتب والقرطاسية', 'description': 'كتب ومواد تعليمية متنوعة'},
            {'name': 'الرياضة واللياقة', 'description': 'معدات رياضية ولياقة بدنية'},
            {'name': 'المنزل والحديقة', 'description': 'أدوات منزلية ومعدات الحديقة'},
        ]

        created_categories = []
        for cat_data in categories_data:
            slug = slugify(cat_data['name'])
            try:
                category, created = Category.objects.get_or_create(
                    name=cat_data['name'],
                    defaults={
                        'slug': slug,
                        'description': cat_data['description']
                    }
                )
            except Exception as e:
                # إذا كان هناك تضارب، نحاول البحث عن الفئة الموجودة
                try:
                    category = Category.objects.get(name=cat_data['name'])
                    created = False
                except Category.DoesNotExist:
                    # إنشاء slug فريد
                    import uuid
                    unique_slug = f"{slug}-{str(uuid.uuid4())[:8]}"
                    category = Category.objects.create(
                        name=cat_data['name'],
                        slug=unique_slug,
                        description=cat_data['description']
                    )
                    created = True
            if created:
                self.stdout.write(f"✅ تم إنشاء الفئة: {category.name}")
                created_categories.append(category)
            else:
                self.stdout.write(f"ℹ️  الفئة موجودة بالفعل: {category.name}")
                created_categories.append(category)

        # إنشاء منتجات تجريبية
        if created_categories:
            products_data = [
                {
                    'name': 'هاتف ذكي متطور',
                    'description': 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',
                    'price': 2500.00,
                    'category': created_categories[0]  # الإلكترونيات
                },
                {
                    'name': 'قميص قطني أنيق',
                    'description': 'قميص قطني عالي الجودة ومريح للارتداء',
                    'price': 150.00,
                    'category': created_categories[1]  # الملابس
                },
                {
                    'name': 'كتاب البرمجة الحديثة',
                    'description': 'دليل شامل لتعلم البرمجة الحديثة',
                    'price': 80.00,
                    'category': created_categories[2]  # الكتب
                },
                {
                    'name': 'حذاء رياضي مريح',
                    'description': 'حذاء رياضي عالي الجودة للجري والتمارين',
                    'price': 300.00,
                    'category': created_categories[3]  # الرياضة
                },
                {
                    'name': 'مصباح LED ذكي',
                    'description': 'مصباح LED قابل للتحكم عن بُعد',
                    'price': 120.00,
                    'category': created_categories[4]  # المنزل
                },
            ]

            for prod_data in products_data:
                slug = slugify(prod_data['name'])
                product, created = Product.objects.get_or_create(
                    slug=slug,
                    defaults={
                        'name': prod_data['name'],
                        'description': prod_data['description'],
                        'price': prod_data['price'],
                        'category': prod_data['category'],
                        'stock': 50,
                        'is_available': True
                    }
                )
                if created:
                    self.stdout.write(f"✅ تم إنشاء المنتج: {product.name}")
                else:
                    self.stdout.write(f"ℹ️  المنتج موجود بالفعل: {product.name}")

        self.stdout.write('🎉 تم إنشاء البيانات التجريبية بنجاح!')
        self.stdout.write(f'📊 إجمالي الفئات: {Category.objects.count()}')
        self.stdout.write(f'📦 إجمالي المنتجات: {Product.objects.count()}')
