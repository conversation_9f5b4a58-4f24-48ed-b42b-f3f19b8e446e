<!DOCTYPE HTML>
<html lang="ar" dir="rtl">
<head>
<meta charset="utf-8">
<meta http-equiv="pragma" content="no-cache" />
<meta http-equiv="cache-control" content="max-age=604800" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

<title>جريت كارت | تقديم الطلب</title>

<link href="images/favicon.ico" rel="shortcut icon" type="image/x-icon">

<!-- jQuery -->
<script src="js/jquery-2.0.0.min.js" type="text/javascript"></script>

<!-- Bootstrap4 files-->
<script src="js/bootstrap.bundle.min.js" type="text/javascript"></script>
<link href="css/bootstrap.css" rel="stylesheet" type="text/css"/>

<!-- Font awesome 5 -->
<link href="fonts/fontawesome/css/all.min.css" type="text/css" rel="stylesheet">

<!-- custom style -->
<link href="css/ui.css" rel="stylesheet" type="text/css"/>
<link href="css/responsive.css" rel="stylesheet" media="only screen and (max-width: 1200px)" />
<link href="css/rtl-arabic.css" rel="stylesheet" type="text/css"/>

<!-- custom javascript -->
<script src="js/script.js" type="text/javascript"></script>
<script src="js/arabic-translation.js" type="text/javascript"></script>

<script type="text/javascript">
/// some script

// jquery ready start
$(document).ready(function() {
	// jQuery code

}); 
// jquery end
</script>

</head>
<body>


<header class="section-header">
<nav class="navbar p-md-0 navbar-expand-sm navbar-light border-bottom">
<div class="container">
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarTop4" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <div class="collapse navbar-collapse" id="navbarTop4">
    <ul class="navbar-nav mr-auto">
    	<li class="nav-item dropdown">
		 	<a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">   Language </a>
		    <ul class="dropdown-menu small">
				<li><a class="dropdown-item" href="#">English</a></li>
				<li><a class="dropdown-item" href="#">Arabic</a></li>
				<li><a class="dropdown-item" href="#">Russian </a></li>
		    </ul>
		</li>
		<li class="nav-item dropdown">
			<a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown"> USD </a>
			<ul class="dropdown-menu small">
				<li><a class="dropdown-item" href="#">EUR</a></li>
				<li><a class="dropdown-item" href="#">AED</a></li>
				<li><a class="dropdown-item" href="#">RUBL </a></li>
		    </ul>
		</li>
    </ul>
    <ul class="navbar-nav">
		<li><a href="#" class="nav-link"> <i class="fa fa-envelope"></i> Email </a></li>
		<li><a href="#" class="nav-link"> <i class="fa fa-phone"></i> Call us </a></li>
	</ul> <!-- list-inline //  -->
  </div> <!-- navbar-collapse .// -->
</div> <!-- container //  -->
</nav>

<section class="header-main border-bottom">
	<div class="container">
<div class="row align-items-center">
	<div class="col-lg-2 col-md-3 col-6">
		<a href="./" class="brand-wrap">
			<img class="logo" src="./images/logo.png">
		</a> <!-- brand-wrap.// -->
	</div>
	<div class="col-lg col-sm col-md col-6 flex-grow-0">
		<div class="category-wrap dropdown d-inline-block float-right">
			<button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown"> 
				<i class="fa fa-bars"></i> All category 
			</button>
			<div class="dropdown-menu">
				<a class="dropdown-item" href="#">Machinery / Mechanical Parts / Tools </a>
				<a class="dropdown-item" href="#">Consumer Electronics / Home Appliances </a>
				<a class="dropdown-item" href="#">Auto / Transportation</a>
				<a class="dropdown-item" href="#">Apparel / Textiles / Timepieces </a>
				<a class="dropdown-item" href="#">Home & Garden / Construction / Lights </a>
				<a class="dropdown-item" href="#">Beauty & Personal Care / Health </a> 
			</div>
		</div>  <!-- category-wrap.// -->
	</div> <!-- col.// -->
	<a href="./store.html" class="btn btn-outline-primary">Store</a>
	<div class="col-lg  col-md-6 col-sm-12 col">
		<form action="#" class="search">
			<div class="input-group w-100">
			    <input type="text" class="form-control" style="width:60%;" placeholder="Search">
			    
			    <div class="input-group-append">
			      <button class="btn btn-primary" type="submit">
			        <i class="fa fa-search"></i>
			      </button>
			    </div>
		    </div>
		</form> <!-- search-wrap .end// -->
	</div> <!-- col.// -->
	<div class="col-lg-3 col-sm-6 col-8 order-2 order-lg-3">
				<div class="d-flex justify-content-end mb-3 mb-lg-0">
					<div class="widget-header">
						<small class="title text-muted">Welcome guest!</small>
						<div> 
							<a href="./signin.html">Sign in</a> <span class="dark-transp"> | </span>
							<a href="./register.html"> Register</a>
						</div>
					</div>
					<a href="./cart.html" class="widget-header pl-3 ml-3">
						<div class="icon icon-sm rounded-circle border"><i class="fa fa-shopping-cart"></i></div>
						<span class="badge badge-pill badge-danger notify">0</span>
					</a>
				</div> <!-- widgets-wrap.// -->
			</div> <!-- col.// -->
</div> <!-- row.// -->
	</div> <!-- container.// -->
</section> <!-- header-main .// -->




</header> <!-- section-header.// -->
<section class="section-content padding-y bg">
<div class="container">



<!-- ============================ COMPONENT 2 ================================= -->
<div class="row">
    <main class="col-md-8">
        <article class="card mb-4">
            <div class="card-body">
                <h4 class="card-title mb-4">مراجعة الطلب</h4>
                {% for cart_item in cart_items %}
                <div class="row">
                    <div class="col-md-6">
                        <figure class="itemside mb-4">
                            <div class="aside"><img src="{{ cart_item.product.image.url }}" class="border img-sm"></div>
                            <figcaption class="info">
                                <p>{{ cart_item.product.name }}</p>
                                <span class="text-muted">{{ cart_item.quantity }}x = {{ cart_item.sub_total }} ج.م</span>
                            </figcaption>
                        </figure>
                    </div>
                </div>
                {% endfor %}
            </div>
        </article>

        <article class="card mb-4">
            <div class="card-body">
                <h4 class="card-title mb-4">معلومات التواصل</h4>
                <form action="{% url 'orders:place_order' %}" method="POST">
                    {% csrf_token %}
                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>الاسم الأول</label>
                            <input type="text" name="first_name" class="form-control" required>
                        </div>
                        <div class="form-group col-sm-6">
                            <label>اسم العائلة</label>
                            <input type="text" name="last_name" class="form-control" required>
                        </div>
                        <div class="form-group col-sm-6">
                            <label>الهاتف</label>
                            <input type="text" name="phone" class="form-control" required>
                        </div>
                        <div class="form-group col-sm-6">
                            <label>البريد الإلكتروني</label>
                            <input type="email" name="email" class="form-control" required>
                        </div>
                        <div class="form-group col-sm-12">
                            <label>العنوان</label>
                            <input type="text" name="address_line_1" class="form-control" required>
                        </div>
                        <div class="form-group col-sm-12">
                            <label>عنوان إضافي (اختياري)</label>
                            <input type="text" name="address_line_2" class="form-control">
                        </div>
                        <div class="form-group col-sm-6">
                            <label>المدينة</label>
                            <input type="text" name="city" class="form-control" required>
                        </div>
                        <div class="form-group col-sm-12">
                            <label>ملاحظات الطلب (اختياري)</label>
                            <textarea name="order_note" rows="2" class="form-control"></textarea>
                        </div>
                    </div>
            </div>
        </article>

        <article class="accordion" id="accordion_pay">
            <div class="card">
                <header class="card-header">
                    <img src="./images/misc/payment-paypal.png" class="float-right" height="24">
                    <label class="form-check collapsed" data-toggle="collapse" data-target="#pay_paynet">
                        <input class="form-check-input" name="payment_method" type="radio" value="PayPal">
                        <h6 class="form-check-label">PayPal</h6>
                    </label>
                </header>
                <div id="pay_paynet" class="collapse show" data-parent="#accordion_pay">
                    <div class="card-body">
                        <p class="text-center text-muted">قم بتوصيل حساب PayPal الخاص بك للدفع. سيتم تحويلك إلى PayPal لإضافة معلومات الدفع الخاصة بك.</p>
                        <p class="text-center">
                            <button type="submit" class="btn btn-primary">تأكيد الطلب</button>
                            <br><br>
                        </p>
                    </div>
                </div>
            </div>
        </article>
        </form>
    </main>
    <aside class="col-md-4">
        <div class="card">
            <div class="card-body">
                <dl class="dlist-align">
                    <dt>السعر الإجمالي:</dt>
                    <dd class="text-right">{{ total }} ج.م</dd>
                </dl>
                <dl class="dlist-align">
                    <dt>الضريبة:</dt>
                    <dd class="text-right">{{ tax }} ج.م</dd>
                </dl>
                <dl class="dlist-align">
                    <dt>المجموع الكلي:</dt>
                    <dd class="text-right text-dark b"><strong>{{ grand_total }} ج.م</strong></dd>
                </dl>
                <hr>
                <p class="text-center mb-3">
                    <img src="./images/misc/payments.png" height="26">
                </p>
            </div>
        </div>
    </aside>
</div>
<!-- ============================ COMPONENT 2 END//  ================================= -->




</div> <!-- container .//  -->
</section>
<!-- ========================= SECTION CONTENT END// ========================= -->


<!-- ========================= SECTION CONTENT END// ========================= -->
</body>
</html>