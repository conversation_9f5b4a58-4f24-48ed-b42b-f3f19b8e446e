#!/usr/bin/env python
"""
Django GreatKart Arabic E-commerce Project Setup Script
"""

import os
import sys
import subprocess

def run_command(command, description):
    """Run a command and print its description"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error in {description}: {e}")
        print(f"Output: {e.output}")
        return False

def setup_project():
    """Setup the Django project"""
    print("🚀 Setting up Django GreatKart Arabic E-commerce Project")
    print("=" * 60)
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected!")
        print("Please activate your virtual environment first:")
        print("   Windows: venv\\Scripts\\activate")
        print("   Linux/Mac: source venv/bin/activate")
        return
    
    # Install requirements
    if not run_command("pip install -r requirements.txt", "Installing requirements"):
        return
    
    # Make migrations
    if not run_command("python manage.py makemigrations", "Creating migrations"):
        return
    
    # Apply migrations
    if not run_command("python manage.py migrate", "Applying migrations"):
        return
    
    # Collect static files
    if not run_command("python manage.py collectstatic --noinput", "Collecting static files"):
        return
    
    print("\n🎉 Project setup completed successfully!")
    print("\nNext steps:")
    print("1. Create a superuser: python manage.py createsuperuser")
    print("2. Run the server: python manage.py runserver")
    print("3. Visit: http://127.0.0.1:8000/")

if __name__ == "__main__":
    setup_project()
