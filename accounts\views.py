from django.shortcuts import render, redirect, get_object_or_404
from .models import Account, UserProfile
from django.contrib import messages, auth
from django.contrib.auth.decorators import login_required
from .forms import RegistrationForm, UserProfileForm, UserForm
from orders.models import Order, OrderProduct
from django.core.mail import EmailMessage
from django.contrib.sites.shortcuts import get_current_site
from django.template.loader import render_to_string
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.contrib.auth.tokens import default_token_generator
from django.utils.encoding import force_bytes, force_str

def register(request):
    if request.method == 'POST':
        first_name = request.POST.get('first_name', '').strip()
        last_name = request.POST.get('last_name', '').strip()
        email = request.POST.get('email', '').strip().lower()
        phone_number = request.POST.get('phone_number', '').strip()
        password = request.POST.get('password', '')
        confirm_password = request.POST.get('confirm_password', '')

        # التحقق من أن جميع الحقول مملوءة
        if not all([first_name, last_name, email, phone_number, password, confirm_password]):
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
            return render(request, 'accounts/register.html')

        # التحقق من طول كلمة المرور
        if len(password) < 8:
            messages.error(request, 'يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل.')
            return render(request, 'accounts/register.html')

        # التحقق من تطابق كلمات المرور
        if password != confirm_password:
            messages.error(request, 'كلمة المرور وتأكيدها غير متطابقتين.')
            return render(request, 'accounts/register.html')

        # التحقق من وجود البريد الإلكتروني
        if Account.objects.filter(email=email).exists():
            messages.error(request, 'البريد الإلكتروني مسجل مسبقاً. يرجى استخدام بريد إلكتروني آخر.')
            return render(request, 'accounts/register.html')

        # التحقق من وجود رقم الهاتف
        if Account.objects.filter(phone_number=phone_number).exists():
            messages.error(request, 'رقم الهاتف مسجل مسبقاً. يرجى استخدام رقم هاتف آخر.')
            return render(request, 'accounts/register.html')

        try:
            # إنشاء اسم المستخدم من البريد الإلكتروني
            username = email.split("@")[0]

            # التأكد من أن اسم المستخدم فريد
            counter = 1
            original_username = username
            while Account.objects.filter(username=username).exists():
                username = f"{original_username}{counter}"
                counter += 1

            # إنشاء المستخدم
            user = Account.objects.create_user(
                first_name=first_name,
                last_name=last_name,
                email=email,
                username=username,
                password=password
            )
            user.phone_number = phone_number
            user.save()

            # إنشاء UserProfile للمستخدم الجديد
            UserProfile.objects.create(user=user)

            messages.success(request, 'تم إنشاء حسابك بنجاح! يمكنك الآن تسجيل الدخول.')
            return redirect('accounts:login')

        except Exception:
            messages.error(request, 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.')
            return render(request, 'accounts/register.html')

    return render(request, 'accounts/register.html')

def login(request):
    if request.method == 'POST':
        email = request.POST['email']
        password = request.POST['password']

        user = auth.authenticate(email=email, password=password)

        if user is not None:
            auth.login(request, user)
            messages.success(request, 'تم تسجيل الدخول بنجاح')
            return redirect('store:home')
        else:
            messages.error(request, 'بيانات الدخول غير صحيحة')
            return redirect('accounts:login')

    return render(request, 'accounts/signin.html')

@login_required(login_url='accounts:login')
def logout(request):
    auth.logout(request)
    messages.success(request, 'تم تسجيل الخروج')
    return redirect('store:home')

@login_required(login_url='login')
def dashboard(request):
    # جلب جميع الطلبات للمستخدم
    orders = Order.objects.filter(user=request.user, is_ordered=True).order_by('-created_at')
    orders_count = orders.count()

    # حساب الطلبات المكتملة
    completed_orders = orders.filter(status='مكتمل').count()

    # حساب الطلبات قيد المعالجة
    pending_orders = orders.filter(status='قيد المعالجة').count()

    # آخر 5 طلبات
    recent_orders = orders[:5]

    context = {
        'orders_count': orders_count,
        'completed_orders': completed_orders,
        'pending_orders': pending_orders,
        'recent_orders': recent_orders,
    }
    return render(request, 'accounts/dashboard.html', context)

@login_required(login_url='login')
def my_orders(request):
    orders = Order.objects.filter(user=request.user, is_ordered=True).order_by('-created_at')
    context = {
        'orders': orders,
    }
    return render(request, 'accounts/my_orders.html', context)

@login_required(login_url='login')
def edit_profile(request):
    # إنشاء أو جلب UserProfile إذا لم يكن موجود
    userprofile, _ = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # تحديث بيانات المستخدم الأساسية
        request.user.first_name = request.POST.get('first_name', '')
        request.user.last_name = request.POST.get('last_name', '')
        request.user.email = request.POST.get('email', '')
        request.user.phone_number = request.POST.get('phone_number', '')
        request.user.save()

        # تحديث بيانات الملف الشخصي
        userprofile.address = request.POST.get('address', '')
        userprofile.gender = request.POST.get('gender', '')

        # تحديث تاريخ الميلاد إذا تم إدخاله
        date_of_birth = request.POST.get('date_of_birth', '')
        if date_of_birth:
            userprofile.date_of_birth = date_of_birth

        userprofile.save()

        messages.success(request, 'تم تحديث ملفك الشخصي بنجاح.')
        return redirect('accounts:edit_profile')

    context = {
        'userprofile': userprofile,
    }
    return render(request, 'accounts/edit_profile.html', context)

@login_required(login_url='login')
def change_password(request):
    if request.method == 'POST':
        current_password = request.POST.get('current_password', '')
        new_password = request.POST.get('new_password', '')
        confirm_password = request.POST.get('confirm_password', '')

        # التحقق من أن جميع الحقول مملوءة
        if not all([current_password, new_password, confirm_password]):
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
            return redirect('accounts:change_password')

        # التحقق من طول كلمة المرور الجديدة
        if len(new_password) < 8:
            messages.error(request, 'يجب أن تحتوي كلمة المرور الجديدة على 8 أحرف على الأقل.')
            return redirect('accounts:change_password')

        # التحقق من تطابق كلمات المرور
        if new_password != confirm_password:
            messages.error(request, 'كلمة المرور الجديدة وتأكيدها غير متطابقتين.')
            return redirect('accounts:change_password')

        # التحقق من كلمة المرور الحالية
        if not request.user.check_password(current_password):
            messages.error(request, 'كلمة المرور الحالية غير صحيحة.')
            return redirect('accounts:change_password')

        # تغيير كلمة المرور
        request.user.set_password(new_password)
        request.user.save()

        # تحديث الجلسة لتجنب تسجيل الخروج التلقائي
        from django.contrib.auth import update_session_auth_hash
        update_session_auth_hash(request, request.user)

        messages.success(request, 'تم تغيير كلمة المرور بنجاح.')
        return redirect('accounts:change_password')

    return render(request, 'accounts/change_password.html')

@login_required(login_url='login')
def order_detail(request, order_id):
    order_detail = OrderProduct.objects.filter(order__order_number=order_id)
    order = Order.objects.get(order_number=order_id)
    subtotal = 0
    for i in order_detail:
        subtotal += i.product_price * i.quantity

    context = {
        'order_detail': order_detail,
        'order': order,
        'subtotal': subtotal,
    }
    return render(request, 'accounts/order_detail.html', context)

def forgot_password(request):
    if request.method == 'POST':
        email = request.POST.get('email', '').strip().lower()

        if not email:
            messages.error(request, 'يرجى إدخال البريد الإلكتروني.')
            return render(request, 'accounts/forgot_password.html')

        # التحقق من وجود المستخدم
        if Account.objects.filter(email=email).exists():
            user = Account.objects.get(email__exact=email)

            # إنشاء رابط إعادة تعيين كلمة المرور
            current_site = get_current_site(request)
            mail_subject = 'إعادة تعيين كلمة المرور - جريت كارت'

            # إنشاء token و uid
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            token = default_token_generator.make_token(user)

            # إنشاء رابط إعادة التعيين
            reset_url = f"http://{current_site.domain}/accounts/reset_password_validate/{uid}/{token}/"

            # محتوى البريد الإلكتروني
            message = render_to_string('accounts/reset_password_email.html', {
                'user': user,
                'domain': current_site.domain,
                'uid': uid,
                'token': token,
                'reset_url': reset_url,
            })

            try:
                # إرسال البريد الإلكتروني
                email_message = EmailMessage(mail_subject, message, to=[email])
                email_message.content_subtype = 'html'
                email_message.send()

                # رسالة نجاح فقط - بدون رابط مباشر لأسباب أمنية
                messages.success(request,
                    f'تم إرسال رابط إعادة تعيين كلمة المرور إلى {email}. '
                    'يرجى التحقق من صندوق الوارد وصندوق الرسائل المزعجة. '
                    'الرابط صالح لمدة 24 ساعة فقط.')

            except Exception as e:
                # في حالة فشل إرسال البريد، نسجل الخطأ ونعرض رسالة عامة
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f'فشل إرسال بريد إعادة تعيين كلمة المرور إلى {email}: {str(e)}')

                messages.error(request,
                    'حدث خطأ أثناء إرسال البريد الإلكتروني. يرجى المحاولة مرة أخرى لاحقاً أو التواصل مع الدعم الفني.')

            return redirect('accounts:forgot_password')
        else:
            messages.error(request, 'البريد الإلكتروني غير مسجل في النظام.')
            return render(request, 'accounts/forgot_password.html')

    return render(request, 'accounts/forgot_password.html')

def reset_password_validate(request, uidb64, token):
    try:
        # فك تشفير uid
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = Account.objects.get(pk=uid)
    except (TypeError, ValueError, OverflowError, Account.DoesNotExist):
        user = None

    # التحقق من صحة الرابط
    if user is not None and default_token_generator.check_token(user, token):
        # حفظ uid في الجلسة لاستخدامه في reset_password
        request.session['uid'] = uid
        messages.success(request, 'يرجى إدخال كلمة المرور الجديدة.')
        return redirect('accounts:reset_password')
    else:
        messages.error(request, 'رابط إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية.')
        return redirect('accounts:forgot_password')

def reset_password(request):
    if request.method == 'POST':
        new_password = request.POST.get('new_password', '')
        confirm_password = request.POST.get('confirm_password', '')

        # التحقق من أن جميع الحقول مملوءة
        if not all([new_password, confirm_password]):
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
            return render(request, 'accounts/reset_password.html')

        # التحقق من طول كلمة المرور الجديدة
        if len(new_password) < 8:
            messages.error(request, 'يجب أن تحتوي كلمة المرور الجديدة على 8 أحرف على الأقل.')
            return render(request, 'accounts/reset_password.html')

        # التحقق من تطابق كلمات المرور
        if new_password != confirm_password:
            messages.error(request, 'كلمة المرور الجديدة وتأكيدها غير متطابقتين.')
            return render(request, 'accounts/reset_password.html')

        # التحقق من وجود uid في الجلسة
        uid = request.session.get('uid')
        if uid:
            try:
                user = Account.objects.get(pk=uid)
                user.set_password(new_password)
                user.save()

                # حذف uid من الجلسة
                del request.session['uid']

                messages.success(request, 'تم تغيير كلمة المرور بنجاح! يمكنك الآن تسجيل الدخول.')
                return redirect('accounts:login')
            except Account.DoesNotExist:
                messages.error(request, 'حدث خطأ. يرجى طلب رابط إعادة تعيين جديد.')
                return redirect('accounts:forgot_password')
        else:
            messages.error(request, 'انتهت صلاحية الجلسة. يرجى طلب رابط إعادة تعيين جديد.')
            return redirect('accounts:forgot_password')

    return render(request, 'accounts/reset_password.html')

def test_email_settings(request):
    """
    صفحة اختبار إعدادات البريد الإلكتروني - للمطورين فقط
    """
    if not request.user.is_staff:
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة.')
        return redirect('store:home')

    from django.conf import settings
    import logging

    context = {
        'email_backend': settings.EMAIL_BACKEND,
        'email_host': settings.EMAIL_HOST,
        'email_port': settings.EMAIL_PORT,
        'email_host_user': settings.EMAIL_HOST_USER,
        'email_use_tls': settings.EMAIL_USE_TLS,
        'default_from_email': settings.DEFAULT_FROM_EMAIL,
    }

    if request.method == 'POST':
        test_email = request.POST.get('test_email', '').strip()

        if not test_email:
            messages.error(request, 'يرجى إدخال بريد إلكتروني للاختبار.')
            return render(request, 'accounts/test_email.html', context)

        try:
            # إرسال بريد تجريبي
            mail_subject = 'اختبار إعدادات البريد الإلكتروني - جريت كارت'
            message = f"""
            <h2>اختبار إعدادات البريد الإلكتروني</h2>
            <p>هذه رسالة اختبار لتأكيد أن إعدادات البريد الإلكتروني تعمل بشكل صحيح.</p>
            <p><strong>إعدادات البريد الحالية:</strong></p>
            <ul>
                <li>Backend: {settings.EMAIL_BACKEND}</li>
                <li>Host: {settings.EMAIL_HOST}</li>
                <li>Port: {settings.EMAIL_PORT}</li>
                <li>TLS: {settings.EMAIL_USE_TLS}</li>
            </ul>
            <p>إذا وصلتك هذه الرسالة، فإن إعدادات البريد تعمل بشكل صحيح!</p>
            """

            email_message = EmailMessage(mail_subject, message, to=[test_email])
            email_message.content_subtype = 'html'
            email_message.send()

            messages.success(request, f'تم إرسال بريد اختبار إلى {test_email} بنجاح!')

        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f'فشل إرسال بريد الاختبار: {str(e)}')
            messages.error(request, f'فشل إرسال البريد: {str(e)}')

    return render(request, 'accounts/test_email.html', context)