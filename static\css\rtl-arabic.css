/* ملف CSS لدعم اللغة العربية واتجاه RTL */

/* تعيين اتجاه الصفحة من اليمين إلى اليسار */
body, html {
    direction: rtl;
    text-align: right;
    font-family: '<PERSON><PERSON><PERSON>', 'Inter', sans-serif;
}

/* Navbar RTL */
.navbar-nav {
    padding-right: 0;
}

.navbar-nav .nav-item {
    margin-left: 1rem;
    margin-right: 0;
}

/* تعديل اتجاه العناصر المختلفة */
.navbar-nav, .dropdown-menu, .list-inline, .list-check {
    padding-right: 0;
}

/* عكس الهوامش والحشوات */
.mr-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

.ml-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

.mr-1, .mr-2, .mr-3, .mr-4, .mr-5 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
}

.ml-1, .ml-2, .ml-3, .ml-4, .ml-5 {
    margin-left: 0 !important;
    margin-right: 0.25rem !important;
}

.pr-1, .pr-2, .pr-3, .pr-4, .pr-5 {
    padding-right: 0 !important;
    padding-left: 0.25rem !important;
}

.pl-1, .pl-2, .pl-3, .pl-4, .pl-5 {
    padding-left: 0 !important;
    padding-right: 0.25rem !important;
}

/* عكس اتجاه الأيقونات */
.fa-arrow-right:before {
    content: "\f060" !important;
}

.fa-arrow-left:before {
    content: "\f061" !important;
}

.fa-long-arrow-alt-right:before {
    content: "\f30a" !important;
}

.fa-long-arrow-alt-left:before {
    content: "\f30b" !important;
}

/* عكس اتجاه الأيقونات الإضافية (Chevrons, Angles) */
.fa-chevron-left:before {
    content: "\f054" !important; /* chevron-right */
}

.fa-chevron-right:before {
    content: "\f053" !important; /* chevron-left */
}

.fa-angle-left:before {
    content: "\f105" !important; /* angle-right */
}

.fa-angle-right:before {
    content: "\f104" !important; /* angle-left */
}

.fa-angle-double-left:before {
    content: "\f101" !important; /* angle-double-right */
}

.fa-angle-double-right:before {
    content: "\f100" !important; /* angle-double-left */
}

/* تعديل اتجاه عناصر القائمة المنسدلة */
.dropdown-menu {
    text-align: right;
}

/* تعديل اتجاه عناصر النموذج */
.form-control {
    text-align: right;
}

/* تعديل اتجاه الأزرار */
.float-right {
    float: left !important;
}

.float-left {
    float: right !important;
}

/* تعديل اتجاه عناصر التنقل */
.navbar-nav {
    direction: rtl;
}

/* تعديل اتجاه عناصر البحث */
.input-group .form-control:not(:last-child) {
    border-radius: 0 0.25rem 0.25rem 0;
}

.input-group .input-group-append {
    margin-right: -1px;
    margin-left: 0;
}

.input-group-append .btn {
    border-radius: 0.25rem 0 0 0.25rem;
}

/* تعديل اتجاه عناصر السلة */
.widget-header .icon {
    margin-left: 10px;
    margin-right: 0;
}

/* تعديل اتجاه عناصر المنتجات */
.card-product-grid .info-wrap {
    text-align: right;
}

/* تعديل اتجاه عناصر التذييل */
.footer-bottom .nav-link {
    text-align: right;
}

/* تعديل اتجاه عناصر الصفحة الرئيسية */
.section-intro, .section-content {
    text-align: right;
}

/* تعديل اتجاه عناصر التنقل الجانبي */
.card-category-list .card-category {
    text-align: right;
}

/* تعديل اتجاه عناصر التصفية */
.filter-content {
    text-align: right;
}

/* تعديل اتجاه عناصر المنتج التفصيلية */
.gallery-wrap, .info-main, .info-aside {
    text-align: right;
}

/* تعديل اتجاه عناصر السلة */
.table-shopping-cart .price {
    text-align: left;
}

/* تعديل اتجاه عناصر الدفع */
.card-header .title {
    text-align: right;
}

/* تعديل اتجاه عناصر صفحة اكتمال الطلب */
.invoice-details .list-unstyled li strong {
    float: right;
    margin-left: 5px; /* لإضافة مسافة بين العنوان والقيمة */
}

.invoice-items .table th,
.invoice-items .table td {
    text-align: right !important;
}

/* الإبقاء على محاذاة أعمدة السعر إلى اليسار */
.invoice-items .table th.text-center:last-child,
.invoice-items .table td.text-center:last-child,
.invoice-items .table tfoot th:last-child {
    text-align: left !important;
}

/* تعديل محاذاة تذييل الفاتورة */
.invoice-footer p {
    text-align: right !important;
}

/* إضافة خط عربي */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

/* RTL Arabic Styles */
body {
    direction: rtl;
    text-align: right;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.dropdown-menu {
    text-align: right;
}

.navbar-nav {
    padding-right: 0;
}

.mr-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

.ml-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

.float-right {
    float: left !important;
}

.float-left {
    float: right !important;
}

.text-right {
    text-align: left !important;
}

.text-left {
    text-align: right !important;
}

.input-group > .form-control:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

.input-group > .input-group-append > .btn {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

/* Fix padding and margins */
.mr-1, .mx-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
.mr-2, .mx-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
.mr-3, .mx-3 { margin-left: 1rem !important; margin-right: 0 !important; }
.mr-4, .mx-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }
.mr-5, .mx-5 { margin-left: 3rem !important; margin-right: 0 !important; }

.ml-1, .mx-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
.ml-2, .mx-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
.ml-3, .mx-3 { margin-right: 1rem !important; margin-left: 0 !important; }
.ml-4, .mx-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }
.ml-5, .mx-5 { margin-right: 3rem !important; margin-left: 0 !important; }

.pr-1, .px-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
.pr-2, .px-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
.pr-3, .px-3 { padding-left: 1rem !important; padding-right: 0 !important; }
.pr-4, .px-4 { padding-left: 1.5rem !important; padding-right: 0 !important; }
.pr-5, .px-5 { padding-left: 3rem !important; padding-right: 0 !important; }

.pl-1, .px-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
.pl-2, .px-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
.pl-3, .px-3 { padding-right: 1rem !important; padding-left: 0 !important; }
.pl-4, .px-4 { padding-right: 1.5rem !important; padding-left: 0 !important; }
.pl-5, .px-5 { padding-right: 3rem !important; padding-left: 0 !important; }

/* Fix icon directions */
.fa-chevron-right:before {
    content: "\f053";
}

.fa-chevron-left:before {
    content: "\f054";
}

/* Fix search box */
.search .input-group .form-control {
    border-radius: 0 4px 4px 0 !important;
}

.search .input-group .btn {
    border-radius: 4px 0 0 4px !important;
}

/* Fix category dropdown */
.category-wrap .dropdown-menu {
    right: 0;
    left: auto;
}

/* Search bar RTL */
.input-group .form-control {
    border-radius: 0 .25rem .25rem 0 !important;
}

.input-group .input-group-append .btn {
    border-radius: .25rem 0 0 .25rem !important;
}

/* Float adjustments */
.float-right {
    float: left !important;
}

.float-left {
    float: right !important;
}

.mr-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

.ml-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

/* Text alignment */
.text-left {
    text-align: right !important;
}

.text-right {
    text-align: left !important;
}

/* Margin and padding */
.mr-1, .mx-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

.mr-2, .mx-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

.mr-3, .mx-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
}

.ml-1, .mx-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
}

.ml-2, .mx-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

.ml-3, .mx-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
}

/* Cart and user widgets */
.widgets-wrap .widget-header {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Product cards */
.card-product-grid .info-wrap {
    text-align: right;
}

/* Pagination */
.pagination {
    padding-right: 0;
}

/* Footer */
.footer-bottom .text-md-right {
    text-align: left !important;
}

/* Form elements */
.form-group label {
    text-align: right;
}

/* Icons */
.fa, .fas {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Bootstrap grid RTL fix */
.offset-1 {
    margin-right: 8.333333%;
    margin-left: 0;
}

.offset-2 {
    margin-right: 16.666667%;
    margin-left: 0;
}

/* Category menu */
.category-wrap .dropdown-toggle::after {
    margin-right: 0.5em;
    margin-left: 0;
}

/* Product details page */
.gallery-wrap .thumbs-wrap {
    margin-right: 0;
    margin-left: 1rem;
}

/* Reviews section */
.rating-stars {
    direction: ltr;
    display: inline-block;
}

/* Order summary */
.summary-footer {
    text-align: left;
}

/* Checkout form */
.form-row > [class*='col-'] {
    padding-right: 5px;
    padding-left: 5px;
}

/* RTL Support for Arabic */
body {
    direction: rtl;
    text-align: right;
}

/* Navbar RTL */
.navbar-nav {
    padding-right: 0;
}

.navbar-nav .nav-item {
    margin-left: 1rem;
    margin-right: 0;
}

.dropdown-menu {
    text-align: right;
}

/* Search bar RTL */
.input-group .form-control {
    border-radius: 0 .25rem .25rem 0 !important;
}

.input-group .input-group-append .btn {
    border-radius: .25rem 0 0 .25rem !important;
}

/* Float adjustments */
.float-right {
    float: left !important;
}

.float-left {
    float: right !important;
}

.mr-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

.ml-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

/* Text alignment */
.text-left {
    text-align: right !important;
}

.text-right {
    text-align: left !important;
}

/* Cart and user widgets */
.widgets-wrap .widget-header {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Product cards */
.card-product-grid .info-wrap {
    text-align: right;
}

/* Product details page */
.gallery-wrap .thumbs-wrap {
    margin-right: 0;
    margin-left: 1rem;
}

/* Reviews section */
.rating-stars {
    direction: ltr;
    display: inline-block;
}

/* Order summary */
.summary-footer {
    text-align: left;
}

/* Form elements */
.form-group label {
    text-align: right;
}

/* Breadcrumb */
.breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

/* Pagination */
.pagination {
    padding-right: 0;
    direction: ltr;
}

/* Card titles and content */
.card-title, .card-text {
    text-align: right;
}

/* List groups */
.list-group {
    padding-right: 0;
}

/* Table */
.table th, .table td {
    text-align: right;
}

/* Modal */
.modal-header .close {
    margin: -1rem auto -1rem -1rem;
}

/* Alert */
.alert-dismissible {
    padding-right: 1.25rem;
    padding-left: 4rem;
}

.alert-dismissible .close {
    left: 0;
    right: auto;
}

/* Input groups */
.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text,
.input-group > .input-group-append:not(:last-child) > .btn,
.input-group > .input-group-append:not(:last-child) > .input-group-text,
.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {
    border-radius: 0 .25rem .25rem 0;
}

.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:not(:first-child) > .btn,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
    border-radius: .25rem 0 0 .25rem;
}

/* Fix for Font Awesome icons */
.fa, .fas, .far, .fab {
    margin-left: 0.3rem;
    margin-right: 0;
}

/* ========================= تحسينات الـ Header الجديد ========================= */

/* الصف الأول - الشعار والبحث والسلة */
.header-main {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.logo {
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

/* شريط البحث المحسن */
.search .form-control {
    font-size: 1rem;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.search .form-control:focus {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

.search .btn {
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.search .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.4);
}

/* أيقونة السلة المحسنة */
.widget-header .icon {
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.widget-header .icon:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,123,255,0.3);
    background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
}

/* الصف الثاني - الروابط والفئات */
.header-nav {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
}

.header-nav .nav-link {
    font-size: 0.95rem;
    padding: 8px 12px;
    border-radius: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.header-nav .nav-link:hover {
    background-color: rgba(0,123,255,0.1);
    transform: translateY(-2px);
}

.header-nav .nav-link:hover i {
    transform: scale(1.2);
}

/* زر الفئات المحسن */
.category-wrap .btn {
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.category-wrap .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

.category-wrap .dropdown-menu {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    margin-top: 10px;
}

.category-wrap .dropdown-item {
    padding: 10px 20px;
    transition: all 0.3s ease;
    border-radius: 10px;
    margin: 2px 5px;
}

.category-wrap .dropdown-item:hover {
    background-color: rgba(0,123,255,0.1);
    transform: translateX(5px);
}

/* تحسينات responsive */
@media (max-width: 768px) {
    .header-main .row > div {
        margin-bottom: 10px;
    }

    .search .form-control {
        font-size: 0.9rem;
        padding: 10px 15px;
    }

    .widget-header .icon {
        width: 45px !important;
        height: 45px !important;
    }

    .header-nav .nav-link {
        font-size: 0.85rem;
        padding: 6px 8px;
    }
}

/* تأثيرات إضافية */
.header-main, .header-nav {
    position: relative;
    overflow: hidden;
}

.header-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(0,123,255,0.02) 50%, transparent 100%);
    pointer-events: none;
}

/* تحسين معلومات المستخدم */
.widget-header {
    transition: all 0.3s ease;
}

.widget-header:hover {
    transform: translateY(-1px);
}

.widget-header a {
    text-decoration: none;
    transition: all 0.3s ease;
}

.widget-header a:hover {
    color: #007bff !important;
    text-decoration: none;
}

/* ========================= تحسينات شريط البحث المحسن ========================= */

.search-container {
    width: 100%;
    max-width: 100%;
}

.search-form {
    width: 100%;
}

.search-input-group {
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.search-input-group:hover {
    box-shadow: 0 4px 20px rgba(0,123,255,0.2);
    border-color: #007bff;
}

.search-input-group:focus-within {
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
    border-color: #007bff;
    transform: translateY(-1px);
}

.search-input-new {
    border: none !important;
    padding: 12px 20px !important;
    font-size: 1rem;
    background: #ffffff;
    outline: none !important;
    box-shadow: none !important;
    border-radius: 25px 0 0 25px !important;
    height: 50px;
}

.search-input-new::placeholder {
    color: #6c757d;
    font-style: normal;
}

.search-input-new:focus {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
}

.search-btn-new {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none !important;
    color: white;
    padding: 12px 20px;
    border-radius: 0 25px 25px 0 !important;
    transition: all 0.3s ease;
    font-weight: 600;
    height: 50px;
    min-width: 60px;
}

.search-btn-new:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    color: white;
    transform: scale(1.02);
}

.search-btn-new:focus {
    box-shadow: none !important;
    outline: none !important;
    color: white;
}

.search-btn-new i {
    font-size: 1.1rem;
}

/* تحسينات إضافية للتخطيط */
@media (max-width: 992px) {
    .search-container {
        margin-bottom: 15px;
    }
}

@media (max-width: 768px) {
    .header-main .row {
        align-items: center;
    }

    .search-container {
        margin-bottom: 10px;
    }
}

/* تحسين التباعد */
.header-main {
    padding: 15px 0;
}

@media (max-width: 576px) {
    .header-main {
        padding: 10px 0;
    }

    .search-container {
        margin-bottom: 8px;
    }
}

/* ========================= تنسيقات صفحة لوحة التحكم ========================= */

/* الشريط الجانبي */
.dashboard-sidebar {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.dashboard-sidebar .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    padding: 15px 20px;
}

.dashboard-sidebar .list-group-item {
    border: none;
    padding: 15px 20px;
    transition: all 0.3s ease;
    border-bottom: 1px solid #f8f9fa;
}

.dashboard-sidebar .list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
    color: #007bff;
}

.dashboard-sidebar .list-group-item.active {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-color: #007bff;
}

.dashboard-sidebar .list-group-item.active:hover {
    transform: none;
    color: white;
}

.dashboard-sidebar .card-footer {
    background-color: #f8f9fa;
    border: none;
    padding: 15px 20px;
}

/* بطاقات الإحصائيات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: none;
    border-radius: 10px 10px 0 0 !important;
    padding: 20px;
}

.card-body {
    padding: 20px;
}

/* بطاقات الإحصائيات الملونة */
.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

/* تحسين الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 15px;
}

.table tbody td {
    padding: 15px;
    border-color: #f8f9fa;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}

/* تحسين الأزرار */
.btn {
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
}

.btn-outline-primary {
    border: 2px solid #007bff;
    color: #007bff;
    background: transparent;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    color: white;
}

/* تحسين الـ badges */
.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
}

.badge-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.badge-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.badge-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

/* تحسين عنوان الصفحة */
.section-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 0 0 20px 20px;
    margin-bottom: 30px;
}

.user-stats small {
    opacity: 0.9;
}

/* تحسينات responsive لصفحة لوحة التحكم */
@media (max-width: 768px) {
    .dashboard-sidebar {
        margin-bottom: 20px;
    }

    .card-body {
        padding: 15px;
    }

    .table thead th,
    .table tbody td {
        padding: 10px;
        font-size: 0.9rem;
    }

    .btn {
        padding: 6px 15px;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .section-header {
        text-align: center;
    }

    .section-header h2 {
        font-size: 1.5rem;
    }

    .card-body {
        padding: 10px;
    }

    .table-responsive {
        font-size: 0.8rem;
    }
}

/* نتائج البحث المباشر */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    z-index: 1000;
    margin-top: 5px;
    max-height: 300px;
    overflow-y: auto;
}

.suggestions-header {
    padding: 15px 20px 10px;
    border-bottom: 1px solid #f8f9fa;
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9rem;
}

.suggestions-list {
    padding: 10px 0;
}

.suggestion-item {
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid #f8f9fa;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* ========================= تحسينات سلة التسوق المتقدمة ========================= */

.user-info {
    text-align: center;
}

.user-greeting {
    padding: 5px 0;
}

.user-links {
    margin-top: 5px;
}

.user-links .btn {
    margin: 0 2px;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.cart-container {
    margin-left: 15px;
}

.cart-link {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: inherit;
    padding: 12px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 3px solid #e9ecef;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    width: 70px;
    height: 70px;
}

.cart-link:hover {
    text-decoration: none;
    color: inherit;
    border-color: #007bff;
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 30px rgba(0,123,255,0.4);
    background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
}

.cart-icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-icon-large {
    font-size: 2rem !important;
    color: #007bff;
    transition: all 0.3s ease;
}

.cart-link:hover .cart-icon-large {
    transform: scale(1.1);
    color: #0056b3;
}

.cart-badge-new {
    position: absolute;
    top: -12px;
    right: -12px;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    border-radius: 50%;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(255,107,53,0.4);
    animation: cartPulse 2s infinite;
}

@keyframes cartPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(255,107,53,0.4);
    }
    50% {
        transform: scale(1.15);
        box-shadow: 0 4px 15px rgba(255,107,53,0.6);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(255,107,53,0.4);
    }
}

/* معاينة سريعة للسلة */
.cart-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 350px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
}

.cart-dropdown-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-dropdown-body {
    max-height: 400px;
    overflow-y: auto;
}

.cart-items {
    padding: 15px;
}

.cart-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f8f9fa;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-image {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
    margin-left: 15px;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: #495057;
}

.cart-item-price {
    font-size: 0.8rem;
    color: #007bff;
    font-weight: bold;
}

.cart-dropdown-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.cart-dropdown-footer .btn {
    margin-bottom: 8px;
    border-radius: 25px;
    font-weight: 600;
}

.empty-cart {
    padding: 30px 20px;
}

.empty-cart i {
    opacity: 0.5;
}

/* تحسينات responsive للبحث والسلة */
@media (max-width: 768px) {
    .search-input-new {
        padding: 10px 15px !important;
        font-size: 0.9rem;
        height: 45px;
    }

    .search-btn-new {
        padding: 10px 15px;
        font-size: 0.9rem;
        height: 45px;
        min-width: 50px;
    }

    .search-input-group {
        border-radius: 22px;
    }

    .cart-link {
        width: 60px;
        height: 60px;
        padding: 10px;
    }

    .cart-icon-large {
        font-size: 1.7rem !important;
    }

    .cart-badge-new {
        width: 22px;
        height: 22px;
        font-size: 0.7rem;
        top: -10px;
        right: -10px;
    }

    .cart-dropdown {
        width: 300px;
        right: -50px;
    }

    .user-links .btn {
        font-size: 0.7rem;
        padding: 3px 8px;
    }
}

@media (max-width: 576px) {
    .search-input-new {
        padding: 8px 12px !important;
        font-size: 0.85rem;
        height: 40px;
    }

    .search-btn-new {
        padding: 8px 12px;
        font-size: 0.85rem;
        height: 40px;
        min-width: 45px;
    }

    .search-input-group {
        border-radius: 20px;
    }

    .cart-link {
        width: 50px;
        height: 50px;
        padding: 8px;
    }

    .cart-icon-large {
        font-size: 1.4rem !important;
    }

    .cart-badge-new {
        width: 20px;
        height: 20px;
        font-size: 0.65rem;
        top: -8px;
        right: -8px;
        border: 2px solid white;
    }

    .cart-dropdown {
        width: 280px;
        right: -100px;
    }
}