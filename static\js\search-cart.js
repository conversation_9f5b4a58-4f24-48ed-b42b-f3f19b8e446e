// ========================= تحسينات شريط البحث والسلة =========================

document.addEventListener('DOMContentLoaded', function() {

    // ========================= شريط البحث المتقدم =========================

    const searchInput = document.getElementById('searchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');
    let searchTimeout;

    // حفظ عمليات البحث الأخيرة في localStorage
    function saveSearchHistory(query) {
        let history = JSON.parse(localStorage.getItem('searchHistory') || '[]');

        // إزالة البحث إذا كان موجود مسبقاً
        history = history.filter(item => item !== query);

        // إضافة البحث الجديد في المقدمة
        history.unshift(query);

        // الاحتفاظ بآخر 5 عمليات بحث فقط
        history = history.slice(0, 5);

        localStorage.setItem('searchHistory', JSON.stringify(history));
    }

    // عرض عمليات البحث الأخيرة
    function showSearchHistory() {
        const history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
        const suggestionsList = document.querySelector('.suggestions-list');

        if (history.length === 0) {
            suggestionsList.innerHTML = '<div class="suggestion-item text-muted">لا توجد عمليات بحث سابقة</div>';
            return;
        }

        suggestionsList.innerHTML = history.map(query =>
            `<div class="suggestion-item" onclick="selectSuggestion('${query}')">
                <i class="fa fa-history text-muted mr-2"></i>
                ${query}
            </div>`
        ).join('');
    }

    // اختيار اقتراح من القائمة
    window.selectSuggestion = function(query) {
        searchInput.value = query;
        searchSuggestions.style.display = 'none';
        searchInput.form.submit();
    }

    // عرض الاقتراحات عند التركيز على البحث
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            showSearchHistory();
            searchSuggestions.style.display = 'block';
        });

        // إخفاء الاقتراحات عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.search-container')) {
                searchSuggestions.style.display = 'none';
            }
        });

        // البحث المباشر أثناء الكتابة
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length < 2) {
                showSearchHistory();
                return;
            }

            // تأخير البحث لتحسين الأداء
            searchTimeout = setTimeout(() => {
                performLiveSearch(query);
            }, 300);
        });

        // حفظ البحث عند الإرسال
        searchInput.form.addEventListener('submit', function() {
            const query = searchInput.value.trim();
            if (query) {
                saveSearchHistory(query);
            }
        });
    }

    // البحث المباشر (يمكن تطويره لاحقاً مع AJAX)
    function performLiveSearch(query) {
        const suggestionsList = document.querySelector('.suggestions-list');

        // محاكاة نتائج البحث (يمكن استبدالها بـ AJAX call)
        const mockResults = [
            'هاتف ذكي',
            'لابتوب',
            'سماعات',
            'ساعة ذكية',
            'كاميرا'
        ].filter(item => item.includes(query));

        if (mockResults.length === 0) {
            suggestionsList.innerHTML = '<div class="suggestion-item text-muted">لا توجد نتائج</div>';
            return;
        }

        suggestionsList.innerHTML = mockResults.map(result =>
            `<div class="suggestion-item" onclick="selectSuggestion('${result}')">
                <i class="fa fa-search text-primary mr-2"></i>
                ${result}
            </div>`
        ).join('');
    }

    // ========================= سلة التسوق المتقدمة =========================

    const cartToggle = document.getElementById('cartToggle');
    const cartDropdown = document.getElementById('cartDropdown');
    let cartDropdownVisible = false;

    // تبديل عرض معاينة السلة
    if (cartToggle) {
        cartToggle.addEventListener('click', function(e) {
            e.preventDefault();

            if (cartDropdownVisible) {
                hideCartDropdown();
            } else {
                showCartDropdown();
            }
        });
    }

    // عرض معاينة السلة
    function showCartDropdown() {
        cartDropdown.style.display = 'block';
        cartDropdownVisible = true;

        // تحميل محتويات السلة
        loadCartItems();

        // إضافة animation
        setTimeout(() => {
            cartDropdown.style.opacity = '1';
            cartDropdown.style.transform = 'translateY(0)';
        }, 10);
    }

    // إخفاء معاينة السلة
    function hideCartDropdown() {
        cartDropdown.style.opacity = '0';
        cartDropdown.style.transform = 'translateY(-10px)';

        setTimeout(() => {
            cartDropdown.style.display = 'none';
            cartDropdownVisible = false;
        }, 300);
    }

    // إخفاء السلة عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.cart-container')) {
            hideCartDropdown();
        }
    });

    // تحميل محتويات السلة (يمكن تطويره مع AJAX)
    function loadCartItems() {
        const cartItems = document.querySelector('.cart-items');

        // محاكاة تحميل البيانات
        setTimeout(() => {
            // يمكن استبدال هذا بـ AJAX call لجلب محتويات السلة الفعلية
            cartItems.innerHTML = `
                <div class="cart-item">
                    <img src="/static/images/products/product-1.jpg" alt="منتج" class="cart-item-image">
                    <div class="cart-item-info">
                        <div class="cart-item-name">هاتف ذكي</div>
                        <div class="cart-item-price">1,200 جنيه</div>
                    </div>
                </div>
                <div class="cart-item">
                    <img src="/static/images/products/product-2.jpg" alt="منتج" class="cart-item-image">
                    <div class="cart-item-info">
                        <div class="cart-item-name">سماعات لاسلكية</div>
                        <div class="cart-item-price">350 جنيه</div>
                    </div>
                </div>
            `;
        }, 500);
    }

    // ========================= تأثيرات بصرية إضافية =========================

    // تأثير hover للبحث
    const searchWrapper = document.querySelector('.search-wrapper');
    if (searchWrapper) {
        searchWrapper.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        searchWrapper.addEventListener('mouseleave', function() {
            if (!searchInput.matches(':focus')) {
                this.style.transform = 'translateY(0)';
            }
        });
    }

    // تأثير نبضة للسلة عند إضافة منتج
    window.animateCartAdd = function() {
        const cartIcon = document.querySelector('.cart-icon-large');
        const cartBadge = document.querySelector('.cart-badge-new');

        if (cartIcon) {
            cartIcon.style.animation = 'none';
            setTimeout(() => {
                cartIcon.style.animation = 'cartPulse 0.8s ease-in-out';
            }, 10);
        }

        if (cartBadge) {
            cartBadge.style.animation = 'none';
            setTimeout(() => {
                cartBadge.style.animation = 'cartPulse 0.8s ease-in-out';
            }, 10);
        }
    }

    // تحديث عداد السلة بشكل ديناميكي
    window.updateCartCount = function(count) {
        const cartBadge = document.querySelector('.cart-badge-new');
        const cartCountSpan = document.querySelector('.cart-count');

        if (cartBadge) {
            if (count > 0) {
                cartBadge.textContent = count;
                cartBadge.style.display = 'flex';
            } else {
                cartBadge.style.display = 'none';
            }
        }

        if (cartCountSpan) {
            cartCountSpan.textContent = count + ' منتج';
        }

        // تشغيل animation
        animateCartAdd();
    }

    // ========================= تحسينات الأداء =========================

    // تحسين الـ scroll للاقتراحات
    const suggestionsList = document.querySelector('.suggestions-list');
    if (suggestionsList) {
        suggestionsList.addEventListener('scroll', function() {
            // يمكن إضافة lazy loading هنا
        });
    }

    // تحسين responsive للسلة
    function adjustCartDropdownPosition() {
        if (window.innerWidth <= 768) {
            cartDropdown.style.right = '-50px';
        } else {
            cartDropdown.style.right = '0';
        }
    }

    window.addEventListener('resize', adjustCartDropdownPosition);
    adjustCartDropdownPosition();

});

// ========================= دوال مساعدة عامة =========================

// تنظيف النصوص
function sanitizeText(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// تنسيق الأرقام
function formatPrice(price) {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP'
    }).format(price);
}

// عرض رسائل التنبيه
function showNotification(message, type = 'success') {
    // يمكن تطوير هذه الدالة لعرض notifications جميلة
    console.log(`${type}: ${message}`);
}
