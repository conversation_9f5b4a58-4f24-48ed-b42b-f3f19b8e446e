{% extends 'base.html' %}
{% load static %}

{% block content %}

<!-- عنوان الصفحة -->
<section class="section-header bg-primary text-white py-4">
	<div class="container">
		<div class="row align-items-center">
			<div class="col-md-8">
				<h2 class="mb-0">
					<i class="fa fa-user-edit mr-3"></i>
					تعديل الملف الشخصي
				</h2>
				<p class="mb-0 mt-2">قم بتحديث معلوماتك الشخصية</p>
			</div>
			<div class="col-md-4 text-md-right">
				<a href="{% url 'accounts:dashboard' %}" class="btn btn-light">
					<i class="fa fa-arrow-right mr-2"></i>
					العودة للوحة التحكم
				</a>
			</div>
		</div>
	</div>
</section>

<!-- ========================= محتوى تعديل الملف الشخصي ========================= -->
<section class="section-content padding-y bg-light">
	<div class="container">
		<div class="row">
			<!-- الشريط الجانبي -->
			<aside class="col-md-3">
				<div class="card dashboard-sidebar">
					<div class="card-header bg-primary text-white">
						<h6 class="mb-0">
							<i class="fa fa-user mr-2"></i>
							حسابي
						</h6>
					</div>
					<div class="list-group list-group-flush">
						<a class="list-group-item list-group-item-action" href="{% url 'accounts:dashboard' %}">
							<i class="fa fa-tachometer-alt mr-2"></i>
							لوحة التحكم
						</a>
						<a class="list-group-item list-group-item-action" href="{% url 'orders:my_orders' %}">
							<i class="fa fa-shopping-bag mr-2"></i>
							طلباتي
						</a>
						<a class="list-group-item list-group-item-action active" href="{% url 'accounts:edit_profile' %}">
							<i class="fa fa-user-edit mr-2"></i>
							تعديل الملف الشخصي
						</a>
						<a class="list-group-item list-group-item-action" href="{% url 'accounts:change_password' %}">
							<i class="fa fa-key mr-2"></i>
							تغيير كلمة المرور
						</a>
					</div>
					<div class="card-footer">
						<a class="btn btn-danger btn-block" href="{% url 'accounts:logout' %}">
							<i class="fa fa-power-off mr-2"></i>
							تسجيل الخروج
						</a>
					</div>
				</div>
			</aside>

			<!-- المحتوى الرئيسي -->
			<main class="col-md-9">
				<!-- رسائل النجاح والخطأ -->
				{% if messages %}
				<div class="row mb-4">
					<div class="col-12">
						{% for message in messages %}
						<div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
							<i class="fa fa-check-circle mr-2"></i>
							{{ message }}
							<button type="button" class="close" data-dismiss="alert" aria-label="Close">
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						{% endfor %}
					</div>
				</div>
				{% endif %}

				<!-- نموذج تعديل الملف الشخصي -->
				<div class="card">
					<div class="card-header">
						<h5 class="mb-0">
							<i class="fa fa-user-edit mr-2"></i>
							المعلومات الشخصية
						</h5>
					</div>
					<div class="card-body">
						<form method="post" class="profile-form">
							{% csrf_token %}
							
							<div class="row">
								<!-- الاسم الأول -->
								<div class="col-md-6 mb-3">
									<label for="first_name" class="form-label">
										<i class="fa fa-user mr-2"></i>
										الاسم الأول <span class="text-danger">*</span>
									</label>
									<input type="text" 
										   class="form-control" 
										   id="first_name" 
										   name="first_name" 
										   value="{{ user.first_name }}" 
										   required>
								</div>

								<!-- الاسم الأخير -->
								<div class="col-md-6 mb-3">
									<label for="last_name" class="form-label">
										<i class="fa fa-user mr-2"></i>
										الاسم الأخير <span class="text-danger">*</span>
									</label>
									<input type="text" 
										   class="form-control" 
										   id="last_name" 
										   name="last_name" 
										   value="{{ user.last_name }}" 
										   required>
								</div>
							</div>

							<div class="row">
								<!-- البريد الإلكتروني -->
								<div class="col-md-6 mb-3">
									<label for="email" class="form-label">
										<i class="fa fa-envelope mr-2"></i>
										البريد الإلكتروني <span class="text-danger">*</span>
									</label>
									<input type="email" 
										   class="form-control" 
										   id="email" 
										   name="email" 
										   value="{{ user.email }}" 
										   required>
								</div>

								<!-- رقم الهاتف -->
								<div class="col-md-6 mb-3">
									<label for="phone_number" class="form-label">
										<i class="fa fa-phone mr-2"></i>
										رقم الهاتف
									</label>
									<input type="tel" 
										   class="form-control" 
										   id="phone_number" 
										   name="phone_number" 
										   value="{{ user.phone_number|default:'' }}" 
										   placeholder="مثال: 01234567890">
								</div>
							</div>

							<!-- معلومات إضافية -->
							<div class="row">
								<div class="col-12">
									<h6 class="mb-3">
										<i class="fa fa-info-circle mr-2"></i>
										معلومات إضافية
									</h6>
								</div>
							</div>

							<div class="row">
								<!-- تاريخ الميلاد -->
								<div class="col-md-6 mb-3">
									<label for="date_of_birth" class="form-label">
										<i class="fa fa-calendar mr-2"></i>
										تاريخ الميلاد
									</label>
									<input type="date" 
										   class="form-control" 
										   id="date_of_birth" 
										   name="date_of_birth" 
										   value="{{ user.userprofile.date_of_birth|date:'Y-m-d'|default:'' }}">
								</div>

								<!-- الجنس -->
								<div class="col-md-6 mb-3">
									<label for="gender" class="form-label">
										<i class="fa fa-venus-mars mr-2"></i>
										الجنس
									</label>
									<select class="form-control" id="gender" name="gender">
										<option value="">اختر الجنس</option>
										<option value="male" {% if user.userprofile.gender == 'male' %}selected{% endif %}>ذكر</option>
										<option value="female" {% if user.userprofile.gender == 'female' %}selected{% endif %}>أنثى</option>
									</select>
								</div>
							</div>

							<!-- العنوان -->
							<div class="row">
								<div class="col-12 mb-3">
									<label for="address" class="form-label">
										<i class="fa fa-map-marker-alt mr-2"></i>
										العنوان
									</label>
									<textarea class="form-control" 
											  id="address" 
											  name="address" 
											  rows="3" 
											  placeholder="أدخل عنوانك الكامل">{{ user.userprofile.address|default:'' }}</textarea>
								</div>
							</div>

							<!-- أزرار الحفظ والإلغاء -->
							<div class="row">
								<div class="col-12">
									<div class="d-flex justify-content-between">
										<div>
											<button type="submit" class="btn btn-primary btn-lg">
												<i class="fa fa-save mr-2"></i>
												حفظ التغييرات
											</button>
											<a href="{% url 'accounts:dashboard' %}" class="btn btn-secondary btn-lg ml-2">
												<i class="fa fa-times mr-2"></i>
												إلغاء
											</a>
										</div>
										<div>
											<a href="{% url 'accounts:change_password' %}" class="btn btn-warning">
												<i class="fa fa-key mr-2"></i>
												تغيير كلمة المرور
											</a>
										</div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>

				<!-- معلومات الحساب -->
				<div class="card mt-4">
					<div class="card-header">
						<h5 class="mb-0">
							<i class="fa fa-info-circle mr-2"></i>
							معلومات الحساب
						</h5>
					</div>
					<div class="card-body">
						<div class="row">
							<div class="col-md-6">
								<table class="table table-borderless">
									<tr>
										<td><strong>تاريخ التسجيل:</strong></td>
										<td>{{ user.date_joined|date:"Y/m/d H:i" }}</td>
									</tr>
									<tr>
										<td><strong>آخر دخول:</strong></td>
										<td>{{ user.last_login|date:"Y/m/d H:i" }}</td>
									</tr>
								</table>
							</div>
							<div class="col-md-6">
								<table class="table table-borderless">
									<tr>
										<td><strong>حالة الحساب:</strong></td>
										<td>
											{% if user.is_active %}
											<span class="badge badge-success">نشط</span>
											{% else %}
											<span class="badge badge-danger">غير نشط</span>
											{% endif %}
										</td>
									</tr>
									<tr>
										<td><strong>البريد مؤكد:</strong></td>
										<td>
											{% if user.is_active %}
											<span class="badge badge-success">مؤكد</span>
											{% else %}
											<span class="badge badge-warning">غير مؤكد</span>
											{% endif %}
										</td>
									</tr>
								</table>
							</div>
						</div>
					</div>
				</div>
			</main>
		</div>
	</div>
</section>
<!-- ========================= نهاية محتوى تعديل الملف الشخصي ========================= -->

{% endblock %}
