{% extends 'base.html' %}
{% load static %}

{% block content %}

<!-- عنوان الصفحة -->
<section class="section-header bg-primary text-white py-4">
	<div class="container">
		<div class="row align-items-center">
			<div class="col-md-8">
				<h2 class="mb-0">
					<i class="fa fa-user-circle mr-3"></i>
					لوحة التحكم - مرحباً {{ user.first_name }}
				</h2>
				<p class="mb-0 mt-2">إدارة حسابك وطلباتك</p>
			</div>
			<div class="col-md-4 text-md-right">
				<div class="user-stats">
					<small class="d-block">عضو منذ: {{ user.date_joined|date:"Y/m/d" }}</small>
					<small class="d-block">آخر دخول: {{ user.last_login|date:"Y/m/d H:i" }}</small>
				</div>
			</div>
		</div>
	</div>
</section>



<!-- ========================= محتوى لوحة التحكم ========================= -->
<section class="section-content padding-y bg-light">
	<div class="container">
		<div class="row">
			<!-- الشريط الجانبي -->
			<aside class="col-md-3">
				<div class="card dashboard-sidebar">
					<div class="card-header bg-primary text-white">
						<h6 class="mb-0">
							<i class="fa fa-user mr-2"></i>
							حسابي
						</h6>
					</div>
					<div class="list-group list-group-flush">
						<a class="list-group-item list-group-item-action active" href="{% url 'accounts:dashboard' %}">
							<i class="fa fa-tachometer-alt mr-2"></i>
							لوحة التحكم
						</a>
						<a class="list-group-item list-group-item-action" href="{% url 'orders:my_orders' %}">
							<i class="fa fa-shopping-bag mr-2"></i>
							طلباتي
						</a>
						<a class="list-group-item list-group-item-action" href="{% url 'accounts:edit_profile' %}">
							<i class="fa fa-user-edit mr-2"></i>
							تعديل الملف الشخصي
						</a>
						<a class="list-group-item list-group-item-action" href="{% url 'accounts:change_password' %}">
							<i class="fa fa-key mr-2"></i>
							تغيير كلمة المرور
						</a>
					</div>
					<div class="card-footer">
						<a class="btn btn-danger btn-block" href="{% url 'accounts:logout' %}">
							<i class="fa fa-power-off mr-2"></i>
							تسجيل الخروج
						</a>
					</div>
				</div>
			</aside>

			<!-- المحتوى الرئيسي -->
			<main class="col-md-9">
				<!-- إحصائيات سريعة -->
				<div class="row mb-4">
					<div class="col-md-3 col-sm-6 mb-3">
						<div class="card text-center bg-primary text-white">
							<div class="card-body">
								<i class="fa fa-shopping-bag fa-2x mb-2"></i>
								<h4 class="mb-0">{{ orders_count|default:0 }}</h4>
								<small>إجمالي الطلبات</small>
							</div>
						</div>
					</div>
					<div class="col-md-3 col-sm-6 mb-3">
						<div class="card text-center bg-success text-white">
							<div class="card-body">
								<i class="fa fa-check-circle fa-2x mb-2"></i>
								<h4 class="mb-0">{{ completed_orders|default:0 }}</h4>
								<small>طلبات مكتملة</small>
							</div>
						</div>
					</div>
					<div class="col-md-3 col-sm-6 mb-3">
						<div class="card text-center bg-warning text-white">
							<div class="card-body">
								<i class="fa fa-clock fa-2x mb-2"></i>
								<h4 class="mb-0">{{ pending_orders|default:0 }}</h4>
								<small>طلبات قيد المعالجة</small>
							</div>
						</div>
					</div>
					<div class="col-md-3 col-sm-6 mb-3">
						<div class="card text-center bg-info text-white">
							<div class="card-body">
								<i class="fa fa-star fa-2x mb-2"></i>
								<h4 class="mb-0">{{ user.userprofile.points|default:0 }}</h4>
								<small>نقاط الولاء</small>
							</div>
						</div>
					</div>
				</div>

				<!-- معلومات الحساب -->
				<div class="card mb-4">
					<div class="card-header">
						<h5 class="mb-0">
							<i class="fa fa-user mr-2"></i>
							معلومات الحساب
						</h5>
					</div>
					<div class="card-body">
						<div class="row">
							<div class="col-md-6">
								<table class="table table-borderless">
									<tr>
										<td><strong>الاسم الكامل:</strong></td>
										<td>{{ user.first_name }} {{ user.last_name }}</td>
									</tr>
									<tr>
										<td><strong>البريد الإلكتروني:</strong></td>
										<td>{{ user.email }}</td>
									</tr>
									<tr>
										<td><strong>رقم الهاتف:</strong></td>
										<td>{{ user.phone_number|default:"غير محدد" }}</td>
									</tr>
								</table>
							</div>
							<div class="col-md-6">
								<table class="table table-borderless">
									<tr>
										<td><strong>تاريخ التسجيل:</strong></td>
										<td>{{ user.date_joined|date:"Y/m/d" }}</td>
									</tr>
									<tr>
										<td><strong>آخر دخول:</strong></td>
										<td>{{ user.last_login|date:"Y/m/d H:i" }}</td>
									</tr>
									<tr>
										<td><strong>حالة الحساب:</strong></td>
										<td>
											{% if user.is_active %}
											<span class="badge badge-success">نشط</span>
											{% else %}
											<span class="badge badge-danger">غير نشط</span>
											{% endif %}
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div class="text-center mt-3">
							<a href="{% url 'accounts:edit_profile' %}" class="btn btn-primary">
								<i class="fa fa-edit mr-2"></i>
								تعديل المعلومات
							</a>
						</div>
					</div>
				</div>

				<!-- آخر الطلبات -->
				<div class="card">
					<div class="card-header d-flex justify-content-between align-items-center">
						<h5 class="mb-0">
							<i class="fa fa-shopping-bag mr-2"></i>
							آخر الطلبات
						</h5>
						<a href="{% url 'orders:my_orders' %}" class="btn btn-sm btn-outline-primary">
							عرض جميع الطلبات
						</a>
					</div>
					<div class="card-body">
						{% if recent_orders %}
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
									<tr>
										<th>رقم الطلب</th>
										<th>التاريخ</th>
										<th>المبلغ</th>
										<th>الحالة</th>
										<th>الإجراءات</th>
									</tr>
								</thead>
								<tbody>
									{% for order in recent_orders %}
									<tr>
										<td>
											<strong>{{ order.order_number }}</strong>
										</td>
										<td>{{ order.created_at|date:"Y/m/d" }}</td>
										<td>{{ order.order_total }} جنيه</td>
										<td>
											{% if order.status == 'مكتمل' %}
											<span class="badge badge-success">{{ order.status }}</span>
											{% elif order.status == 'قيد المعالجة' %}
											<span class="badge badge-warning">{{ order.status }}</span>
											{% else %}
											<span class="badge badge-info">{{ order.status }}</span>
											{% endif %}
										</td>
										<td>
											<a href="{% url 'accounts:order_detail' order.order_number %}" class="btn btn-sm btn-outline-primary">
												تفاصيل
											</a>
										</td>
									</tr>
									{% endfor %}
								</tbody>
							</table>
						</div>
						{% else %}
						<div class="text-center py-4">
							<i class="fa fa-shopping-bag fa-3x text-muted mb-3"></i>
							<h5 class="text-muted">لا توجد طلبات بعد</h5>
							<p class="text-muted">ابدأ التسوق الآن واستمتع بتجربة رائعة!</p>
							<a href="{% url 'store:store' %}" class="btn btn-primary">
								<i class="fa fa-shopping-cart mr-2"></i>
								ابدأ التسوق
							</a>
						</div>
						{% endif %}
					</div>
				</div>
			</main>
		</div>
	</div>
</section>
<!-- ========================= نهاية محتوى لوحة التحكم ========================= -->


{% endblock %}