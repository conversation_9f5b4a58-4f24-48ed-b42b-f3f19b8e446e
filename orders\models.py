from django.db import models
from accounts.models import Account
from store.models import Product

class Payment(models.Model):
    PAYMENT_STATUS = (
        ('Pending', 'في انتظار الدفع'),
        ('Completed', 'تم الدفع'),
        ('Failed', 'فشل الدفع'),
        ('Refunded', 'تم الاسترداد'),
    )

    user = models.ForeignKey(Account, verbose_name='المستخدم', on_delete=models.CASCADE)
    payment_id = models.CharField('معرف الدفع', max_length=100)
    payment_method = models.CharField('طريقة الدفع', max_length=100)
    amount_paid = models.CharField('المبلغ المدفوع', max_length=100)
    status = models.CharField('حالة الدفع', max_length=15, choices=PAYMENT_STATUS, default='Pending')
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)

    class Meta:
        verbose_name = 'دفعة'
        verbose_name_plural = 'الدفعات'

    def __str__(self):
        return self.payment_id

class Order(models.Model):
    STATUS = (
        ('Pending', 'في انتظار التأكيد'),
        ('Confirmed', 'مؤكد'),
        ('Processing', 'قيد التحضير'),
        ('Shipped', 'تم الشحن'),
        ('Delivered', 'تم التسليم'),
        ('Paid', 'تم الدفع'),
        ('Completed', 'مكتمل'),
        ('Cancelled', 'ملغي'),
    )

    user = models.ForeignKey(Account, verbose_name='المستخدم', on_delete=models.SET_NULL, null=True)
    payment = models.ForeignKey(Payment, verbose_name='الدفع', on_delete=models.SET_NULL, blank=True, null=True)
    order_number = models.CharField('رقم الطلب', max_length=20)
    first_name = models.CharField('الاسم الأول', max_length=50)
    last_name = models.CharField('اسم العائلة', max_length=50)
    phone = models.CharField('رقم الهاتف', max_length=15)
    email = models.EmailField('البريد الإلكتروني', max_length=50)
    address_line_1 = models.CharField('العنوان 1', max_length=50)
    address_line_2 = models.CharField('العنوان 2', max_length=50, blank=True)
    city = models.CharField('المدينة', max_length=50)
    order_note = models.CharField('ملاحظات الطلب', max_length=100, blank=True)
    order_total = models.FloatField('إجمالي الطلب')
    tax = models.FloatField('الضريبة')
    status = models.CharField('الحالة', max_length=15, choices=STATUS, default='Pending')
    ip = models.CharField('عنوان IP', blank=True, max_length=20)
    is_ordered = models.BooleanField('تم الطلب', default=False)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)

    # حقول إضافية للتتبع
    confirmed_at = models.DateTimeField('تاريخ التأكيد', null=True, blank=True)
    shipped_at = models.DateTimeField('تاريخ الشحن', null=True, blank=True)
    delivered_at = models.DateTimeField('تاريخ التسليم', null=True, blank=True)
    tracking_number = models.CharField('رقم التتبع', max_length=100, blank=True, null=True)
    admin_notes = models.TextField('ملاحظات الإدارة', blank=True, null=True)

    class Meta:
        verbose_name = 'طلب'
        verbose_name_plural = 'الطلبات'

    def full_name(self):
        return f'{self.first_name} {self.last_name}'

    def full_address(self):
        return f'{self.address_line_1} {self.address_line_2}'

    def is_cash_on_delivery(self):
        """تحقق من كون الطلب دفع عند الاستلام"""
        return self.payment and self.payment.payment_method == 'الدفع عند الاستلام'

    def can_be_cancelled(self):
        """تحقق من إمكانية إلغاء الطلب"""
        return self.status in ['Pending', 'Confirmed']

    def get_status_color(self):
        """إرجاع لون مناسب لحالة الطلب"""
        colors = {
            'Pending': 'warning',
            'Confirmed': 'info',
            'Processing': 'primary',
            'Shipped': 'secondary',
            'Delivered': 'success',
            'Paid': 'info',
            'Completed': 'success',
            'Cancelled': 'danger',
        }
        return colors.get(self.status, 'secondary')

    def get_payment_status_display(self):
        """إرجاع حالة الدفع بناءً على نوع الدفع وحالة الطلب"""
        if not self.payment:
            return 'غير محدد'

        # للدفع عند الاستلام
        if self.is_cash_on_delivery():
            if self.status in ['Completed', 'Paid']:
                return 'تم الدفع'
            elif self.status == 'Delivered':
                return 'تم الدفع عند التسليم'
            elif self.status in ['Pending', 'Confirmed']:
                return 'في انتظار التأكيد'
            elif self.status in ['Processing', 'Shipped']:
                return 'سيتم الدفع عند التسليم'
            else:
                return 'في انتظار الدفع'

        # للدفع الإلكتروني
        return self.payment.get_status_display()

    def get_payment_status_color(self):
        """إرجاع لون مناسب لحالة الدفع"""
        if not self.payment:
            return 'secondary'

        # للدفع عند الاستلام
        if self.is_cash_on_delivery():
            if self.status in ['Completed', 'Paid']:
                return 'success'  # تم الدفع
            elif self.status == 'Delivered':
                return 'success'  # تم الدفع عند التسليم
            elif self.status in ['Pending', 'Confirmed']:
                return 'warning'  # في انتظار التأكيد
            elif self.status in ['Processing', 'Shipped']:
                return 'info'     # سيتم الدفع عند التسليم
            else:
                return 'warning'  # في انتظار الدفع

        # للدفع الإلكتروني
        payment_colors = {
            'Pending': 'warning',
            'Completed': 'success',
            'Failed': 'danger',
            'Refunded': 'secondary',
        }
        return payment_colors.get(self.payment.status, 'secondary')

    def __str__(self):
        return f'طلب #{self.order_number} - {self.first_name}'

class OrderProduct(models.Model):
    order = models.ForeignKey(Order, verbose_name='الطلب', on_delete=models.CASCADE)
    payment = models.ForeignKey(Payment, verbose_name='الدفع', on_delete=models.SET_NULL, blank=True, null=True)
    user = models.ForeignKey(Account, verbose_name='المستخدم', on_delete=models.CASCADE)
    product = models.ForeignKey(Product, verbose_name='المنتج', on_delete=models.CASCADE)
    quantity = models.IntegerField('الكمية')
    product_price = models.FloatField('سعر المنتج')
    ordered = models.BooleanField('تم الطلب', default=False)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)

    class Meta:
        verbose_name = 'منتج الطلب'
        verbose_name_plural = 'منتجات الطلب'

    def __str__(self):
        return self.product.name