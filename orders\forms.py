from django import forms
from django.core.validators import RegexValidator
from .models import Order

class OrderForm(forms.ModelForm):
    # إضافة validator لرقم الهاتف
    phone_validator = RegexValidator(
        regex=r'^[\+]?[0-9]{10,15}$',
        message='يرجى إدخال رقم هاتف صحيح (10-15 رقم، يمكن أن يبدأ بـ +)'
    )

    phone = forms.CharField(
        label='رقم الهاتف',
        max_length=15,
        validators=[phone_validator],
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'type': 'tel',
            'pattern': r'[0-9+\-\s\(\)]+',
            'placeholder': 'مثال: 01234567890 أو +201234567890',
            'title': 'يرجى إدخال أرقام فقط (يمكن أن يحتوي على + في البداية)'
        })
    )

    class Meta:
        model = Order
        fields = ['first_name', 'last_name', 'phone', 'email', 'address_line_1', 'address_line_2', 'city', 'order_note']

    def __init__(self, *args, **kwargs):
        super(OrderForm, self).__init__(*args, **kwargs)
        for field in self.fields:
            if field != 'phone':  # تم تخصيص phone field أعلاه
                self.fields[field].widget.attrs['class'] = 'form-control'
            if field != 'order_note' and field != 'address_line_2':
                self.fields[field].required = True

    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if phone:
            # التحقق من أن الرقم يحتوي على أرقام فقط (باستثناء + في البداية)
            if not phone.replace('+', '').replace(' ', '').replace('-', '').replace('(', '').replace(')', '').isdigit():
                raise forms.ValidationError('رقم الهاتف يجب أن يحتوي على أرقام فقط')
            # التحقق من طول الرقم
            digits_only = ''.join(filter(str.isdigit, phone))
            if len(digits_only) < 10 or len(digits_only) > 15:
                raise forms.ValidationError('رقم الهاتف يجب أن يكون بين 10 و 15 رقم')
        return phone