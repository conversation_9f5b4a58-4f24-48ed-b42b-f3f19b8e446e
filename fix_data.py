#!/usr/bin/env python
"""
Script to fix all data issues in the Django GreatKart project
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'greatkart.settings')
django.setup()

from store.models import Category, Product
import uuid

def fix_categories():
    """Fix categories with empty slugs"""
    print("🔧 Fixing categories...")
    
    categories_with_empty_slug = Category.objects.filter(slug='')
    print(f"Found {categories_with_empty_slug.count()} categories with empty slug")
    
    for category in categories_with_empty_slug:
        category.slug = f'category-{str(uuid.uuid4())[:8]}'
        category.save()
        print(f"✅ Fixed category: {category.name} -> {category.slug}")
    
    # List all categories
    print("\n📋 All categories:")
    for cat in Category.objects.all():
        print(f"- {cat.name} ({cat.slug})")

def fix_products():
    """Fix products with empty slugs"""
    print("\n🔧 Fixing products...")
    
    products_with_empty_slug = Product.objects.filter(slug='')
    print(f"Found {products_with_empty_slug.count()} products with empty slug")
    
    for product in products_with_empty_slug:
        product.slug = f'product-{str(uuid.uuid4())[:8]}'
        product.save()
        print(f"✅ Fixed product: {product.name} -> {product.slug}")
    
    # List all products
    print("\n📋 All products:")
    for prod in Product.objects.all():
        print(f"- {prod.name} ({prod.slug}) - Category: {prod.category.name if prod.category else 'None'}")

def test_urls():
    """Test that all URLs work"""
    print("\n🧪 Testing URLs...")
    
    # Test categories
    for category in Category.objects.all():
        try:
            url = category.get_url()
            if url == '#':
                print(f"⚠️  Category {category.name} has safe URL (#)")
            else:
                print(f"✅ Category {category.name}: {url}")
        except Exception as e:
            print(f"❌ Error with category {category.name}: {e}")
    
    # Test products
    for product in Product.objects.all():
        try:
            url = product.get_url()
            if url == '#':
                print(f"⚠️  Product {product.name} has safe URL (#)")
            else:
                print(f"✅ Product {product.name}: {url}")
        except Exception as e:
            print(f"❌ Error with product {product.name}: {e}")

def main():
    print("🚀 Starting data fix script...")
    print("=" * 50)
    
    fix_categories()
    fix_products()
    test_urls()
    
    print("\n🎉 Data fix completed!")
    print("=" * 50)

if __name__ == "__main__":
    main()
