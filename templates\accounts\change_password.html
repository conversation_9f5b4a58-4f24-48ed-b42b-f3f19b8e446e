{% extends 'base.html' %}
{% load static %}

{% block content %}

<!-- عنوان الصفحة -->
<section class="section-header bg-primary text-white py-4">
	<div class="container">
		<div class="row align-items-center">
			<div class="col-md-8">
				<h2 class="mb-0">
					<i class="fa fa-key mr-3"></i>
					تغيير كلمة المرور
				</h2>
				<p class="mb-0 mt-2">قم بتحديث كلمة المرور الخاصة بك</p>
			</div>
			<div class="col-md-4 text-md-right">
				<a href="{% url 'accounts:dashboard' %}" class="btn btn-light">
					<i class="fa fa-arrow-right mr-2"></i>
					العودة للوحة التحكم
				</a>
			</div>
		</div>
	</div>
</section>

<!-- ========================= محتوى تغيير كلمة المرور ========================= -->
<section class="section-content padding-y bg-light">
	<div class="container">
		<div class="row">
			<!-- الشريط الجانبي -->
			<aside class="col-md-3">
				<div class="card dashboard-sidebar">
					<div class="card-header bg-primary text-white">
						<h6 class="mb-0">
							<i class="fa fa-user mr-2"></i>
							حسابي
						</h6>
					</div>
					<div class="list-group list-group-flush">
						<a class="list-group-item list-group-item-action" href="{% url 'accounts:dashboard' %}">
							<i class="fa fa-tachometer-alt mr-2"></i>
							لوحة التحكم
						</a>
						<a class="list-group-item list-group-item-action" href="{% url 'orders:my_orders' %}">
							<i class="fa fa-shopping-bag mr-2"></i>
							طلباتي
						</a>
						<a class="list-group-item list-group-item-action" href="{% url 'accounts:edit_profile' %}">
							<i class="fa fa-user-edit mr-2"></i>
							تعديل الملف الشخصي
						</a>
						<a class="list-group-item list-group-item-action active" href="{% url 'accounts:change_password' %}">
							<i class="fa fa-key mr-2"></i>
							تغيير كلمة المرور
						</a>
					</div>
					<div class="card-footer">
						<a class="btn btn-danger btn-block" href="{% url 'accounts:logout' %}">
							<i class="fa fa-power-off mr-2"></i>
							تسجيل الخروج
						</a>
					</div>
				</div>
			</aside>

			<!-- المحتوى الرئيسي -->
			<main class="col-md-9">
				<!-- رسائل النجاح والخطأ -->
				{% if messages %}
				<div class="row mb-4">
					<div class="col-12">
						{% for message in messages %}
						<div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
							<i class="fa fa-check-circle mr-2"></i>
							{{ message }}
							<button type="button" class="close" data-dismiss="alert" aria-label="Close">
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						{% endfor %}
					</div>
				</div>
				{% endif %}

				<!-- نموذج تغيير كلمة المرور -->
				<div class="card">
					<div class="card-header">
						<h5 class="mb-0">
							<i class="fa fa-key mr-2"></i>
							تغيير كلمة المرور
						</h5>
					</div>
					<div class="card-body">
						<div class="row mb-4">
							<div class="col-12">
								<div class="alert alert-info">
									<i class="fa fa-info-circle mr-2"></i>
									<strong>تعليمات الأمان:</strong>
									<ul class="mb-0 mt-2">
										<li>يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل</li>
										<li>استخدم مزيج من الأحرف الكبيرة والصغيرة والأرقام</li>
										<li>تجنب استخدام معلومات شخصية واضحة</li>
										<li>لا تشارك كلمة المرور مع أي شخص آخر</li>
									</ul>
								</div>
							</div>
						</div>

						<form method="post" class="password-form">
							{% csrf_token %}
							
							<!-- كلمة المرور الحالية -->
							<div class="form-group mb-4">
								<label for="current_password" class="form-label">
									<i class="fa fa-lock mr-2"></i>
									كلمة المرور الحالية <span class="text-danger">*</span>
								</label>
								<div class="input-group">
									<input type="password" 
										   class="form-control" 
										   id="current_password" 
										   name="current_password" 
										   required
										   placeholder="أدخل كلمة المرور الحالية">
									<div class="input-group-append">
										<button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
											<i class="fa fa-eye" id="current_password_icon"></i>
										</button>
									</div>
								</div>
							</div>

							<!-- كلمة المرور الجديدة -->
							<div class="form-group mb-4">
								<label for="new_password" class="form-label">
									<i class="fa fa-key mr-2"></i>
									كلمة المرور الجديدة <span class="text-danger">*</span>
								</label>
								<div class="input-group">
									<input type="password" 
										   class="form-control" 
										   id="new_password" 
										   name="new_password" 
										   required
										   minlength="8"
										   placeholder="أدخل كلمة المرور الجديدة">
									<div class="input-group-append">
										<button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
											<i class="fa fa-eye" id="new_password_icon"></i>
										</button>
									</div>
								</div>
								<small class="form-text text-muted">
									يجب أن تحتوي على 8 أحرف على الأقل
								</small>
							</div>

							<!-- تأكيد كلمة المرور الجديدة -->
							<div class="form-group mb-4">
								<label for="confirm_password" class="form-label">
									<i class="fa fa-check-circle mr-2"></i>
									تأكيد كلمة المرور الجديدة <span class="text-danger">*</span>
								</label>
								<div class="input-group">
									<input type="password" 
										   class="form-control" 
										   id="confirm_password" 
										   name="confirm_password" 
										   required
										   minlength="8"
										   placeholder="أعد إدخال كلمة المرور الجديدة">
									<div class="input-group-append">
										<button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
											<i class="fa fa-eye" id="confirm_password_icon"></i>
										</button>
									</div>
								</div>
								<small class="form-text text-muted">
									يجب أن تطابق كلمة المرور الجديدة
								</small>
							</div>

							<!-- مؤشر قوة كلمة المرور -->
							<div class="form-group mb-4">
								<label class="form-label">قوة كلمة المرور:</label>
								<div class="progress" style="height: 8px;">
									<div class="progress-bar" id="password-strength" role="progressbar" style="width: 0%"></div>
								</div>
								<small class="form-text" id="password-strength-text">أدخل كلمة المرور لرؤية قوتها</small>
							</div>

							<!-- أزرار الحفظ والإلغاء -->
							<div class="row">
								<div class="col-12">
									<div class="d-flex justify-content-between">
										<div>
											<button type="submit" class="btn btn-primary btn-lg">
												<i class="fa fa-save mr-2"></i>
												تغيير كلمة المرور
											</button>
											<a href="{% url 'accounts:dashboard' %}" class="btn btn-secondary btn-lg ml-2">
												<i class="fa fa-times mr-2"></i>
												إلغاء
											</a>
										</div>
										<div>
											<a href="{% url 'accounts:edit_profile' %}" class="btn btn-info">
												<i class="fa fa-user-edit mr-2"></i>
												تعديل الملف الشخصي
											</a>
										</div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>

				<!-- نصائح الأمان -->
				<div class="card mt-4">
					<div class="card-header">
						<h5 class="mb-0">
							<i class="fa fa-shield-alt mr-2"></i>
							نصائح الأمان
						</h5>
					</div>
					<div class="card-body">
						<div class="row">
							<div class="col-md-6">
								<h6><i class="fa fa-check text-success mr-2"></i>كلمة مرور قوية:</h6>
								<ul class="list-unstyled">
									<li><i class="fa fa-check-circle text-success mr-2"></i>8 أحرف على الأقل</li>
									<li><i class="fa fa-check-circle text-success mr-2"></i>أحرف كبيرة وصغيرة</li>
									<li><i class="fa fa-check-circle text-success mr-2"></i>أرقام ورموز</li>
									<li><i class="fa fa-check-circle text-success mr-2"></i>غير متوقعة</li>
								</ul>
							</div>
							<div class="col-md-6">
								<h6><i class="fa fa-times text-danger mr-2"></i>تجنب:</h6>
								<ul class="list-unstyled">
									<li><i class="fa fa-times-circle text-danger mr-2"></i>المعلومات الشخصية</li>
									<li><i class="fa fa-times-circle text-danger mr-2"></i>كلمات المرور الشائعة</li>
									<li><i class="fa fa-times-circle text-danger mr-2"></i>التواريخ المهمة</li>
									<li><i class="fa fa-times-circle text-danger mr-2"></i>أسماء الأشخاص</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</main>
		</div>
	</div>
</section>
<!-- ========================= نهاية محتوى تغيير كلمة المرور ========================= -->

<script>
// إظهار/إخفاء كلمة المرور
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// فحص قوة كلمة المرور
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.getElementById('password-strength');
    const strengthText = document.getElementById('password-strength-text');
    
    let strength = 0;
    let feedback = [];
    
    // طول كلمة المرور
    if (password.length >= 8) {
        strength += 20;
    } else {
        feedback.push('8 أحرف على الأقل');
    }
    
    // أحرف صغيرة
    if (/[a-z]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('أحرف صغيرة');
    }
    
    // أحرف كبيرة
    if (/[A-Z]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('أحرف كبيرة');
    }
    
    // أرقام
    if (/[0-9]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('أرقام');
    }
    
    // رموز خاصة
    if (/[^A-Za-z0-9]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('رموز خاصة');
    }
    
    // تحديث شريط القوة
    strengthBar.style.width = strength + '%';
    
    if (strength < 40) {
        strengthBar.className = 'progress-bar bg-danger';
        strengthText.textContent = 'ضعيفة - تحتاج: ' + feedback.join(', ');
        strengthText.className = 'form-text text-danger';
    } else if (strength < 80) {
        strengthBar.className = 'progress-bar bg-warning';
        strengthText.textContent = 'متوسطة - تحتاج: ' + feedback.join(', ');
        strengthText.className = 'form-text text-warning';
    } else {
        strengthBar.className = 'progress-bar bg-success';
        strengthText.textContent = 'قوية - ممتازة!';
        strengthText.className = 'form-text text-success';
    }
});

// التحقق من تطابق كلمات المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && newPassword !== confirmPassword) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
        if (confirmPassword) {
            this.classList.add('is-valid');
        }
    }
});
</script>

{% endblock %}
