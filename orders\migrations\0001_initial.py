# Generated by Django 5.2.1 on 2025-05-24 08:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('store', '0002_alter_category_options_alter_product_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=20, verbose_name='رقم الطلب')),
                ('first_name', models.CharField(max_length=50, verbose_name='الاسم الأول')),
                ('last_name', models.CharField(max_length=50, verbose_name='اسم العائلة')),
                ('phone', models.Char<PERSON><PERSON>(max_length=15, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(max_length=50, verbose_name='البريد الإلكتروني')),
                ('address_line_1', models.CharField(max_length=50, verbose_name='العنوان 1')),
                ('address_line_2', models.CharField(blank=True, max_length=50, verbose_name='العنوان 2')),
                ('city', models.CharField(max_length=50, verbose_name='المدينة')),
                ('order_note', models.CharField(blank=True, max_length=100, verbose_name='ملاحظات الطلب')),
                ('order_total', models.FloatField(verbose_name='إجمالي الطلب')),
                ('tax', models.FloatField(verbose_name='الضريبة')),
                ('status', models.CharField(choices=[('New', 'جديد'), ('Accepted', 'مقبول'), ('Completed', 'مكتمل'), ('Cancelled', 'ملغي')], default='New', max_length=10, verbose_name='الحالة')),
                ('ip', models.CharField(blank=True, max_length=20, verbose_name='عنوان IP')),
                ('is_ordered', models.BooleanField(default=False, verbose_name='تم الطلب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'طلب',
                'verbose_name_plural': 'الطلبات',
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_id', models.CharField(max_length=100, verbose_name='معرف الدفع')),
                ('payment_method', models.CharField(max_length=100, verbose_name='طريقة الدفع')),
                ('amount_paid', models.CharField(max_length=100, verbose_name='المبلغ المدفوع')),
                ('status', models.CharField(max_length=100, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'الدفعات',
            },
        ),
        migrations.CreateModel(
            name='OrderProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(verbose_name='الكمية')),
                ('product_price', models.FloatField(verbose_name='سعر المنتج')),
                ('ordered', models.BooleanField(default=False, verbose_name='تم الطلب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='orders.order', verbose_name='الطلب')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.product', verbose_name='المنتج')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
                ('payment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='orders.payment', verbose_name='الدفع')),
            ],
            options={
                'verbose_name': 'منتج الطلب',
                'verbose_name_plural': 'منتجات الطلب',
            },
        ),
        migrations.AddField(
            model_name='order',
            name='payment',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='orders.payment', verbose_name='الدفع'),
        ),
    ]
