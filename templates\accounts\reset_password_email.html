<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - جريت كارت</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
        }
        .message {
            font-size: 16px;
            line-height: 1.8;
            color: #555;
            margin-bottom: 30px;
        }
        .reset-button {
            text-align: center;
            margin: 30px 0;
        }
        .reset-button a {
            display: inline-block;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 40px;
            text-decoration: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
        }
        .reset-button a:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        .alternative-link {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .alternative-link p {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #666;
        }
        .alternative-link code {
            background-color: #e9ecef;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            word-break: break-all;
            display: block;
            margin-top: 10px;
        }
        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .security-notice h3 {
            color: #856404;
            margin: 0 0 15px 0;
            font-size: 16px;
        }
        .security-notice ul {
            margin: 0;
            padding-right: 20px;
            color: #856404;
        }
        .security-notice li {
            margin-bottom: 8px;
            font-size: 14px;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        .footer p {
            margin: 0;
            font-size: 14px;
            color: #666;
        }
        .footer .company-info {
            margin-top: 15px;
            font-size: 12px;
            color: #999;
        }
        .icon {
            font-size: 24px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🔐 إعادة تعيين كلمة المرور</h1>
            <p>جريت كارت - متجرك الإلكتروني المفضل</p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">
                مرحباً {{ user.first_name }} {{ user.last_name }}،
            </div>

            <div class="message">
                <p>تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك في جريت كارت.</p>
                
                <p>إذا كنت قد طلبت إعادة تعيين كلمة المرور، يرجى الضغط على الزر أدناه لإنشاء كلمة مرور جديدة:</p>
            </div>

            <!-- Reset Button -->
            <div class="reset-button">
                <a href="{{ reset_url }}">🔑 إعادة تعيين كلمة المرور</a>
            </div>

            <!-- Alternative Link -->
            <div class="alternative-link">
                <p><strong>لا يعمل الزر؟</strong> انسخ والصق الرابط التالي في متصفحك:</p>
                <code>{{ reset_url }}</code>
            </div>

            <!-- Security Notice -->
            <div class="security-notice">
                <h3>🛡️ ملاحظات أمنية مهمة:</h3>
                <ul>
                    <li>هذا الرابط صالح لمدة 24 ساعة فقط</li>
                    <li>لا تشارك هذا الرابط مع أي شخص آخر</li>
                    <li>إذا لم تطلب إعادة تعيين كلمة المرور، تجاهل هذه الرسالة</li>
                    <li>تأكد من اختيار كلمة مرور قوية وآمنة</li>
                    <li>لا تستخدم نفس كلمة المرور في مواقع أخرى</li>
                </ul>
            </div>

            <div class="message">
                <p>إذا واجهت أي مشاكل أو لديك أسئلة، لا تتردد في التواصل مع فريق الدعم الفني.</p>
                
                <p>شكراً لك لاختيارك جريت كارت!</p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>فريق جريت كارت</strong></p>
            <div class="company-info">
                <p>هذه رسالة تلقائية، يرجى عدم الرد عليها</p>
                <p>© 2024 جريت كارت. جميع الحقوق محفوظة</p>
                <p>إذا لم تعد ترغب في تلقي هذه الرسائل، يمكنك إلغاء الاشتراك من إعدادات حسابك</p>
            </div>
        </div>
    </div>
</body>
</html>
