# Production Requirements
# Install with: pip install -r requirements-prod.txt

# Include base requirements
-r requirements.txt

# Production Web Server
gunicorn==21.2.0

# Database
psycopg2-binary==2.9.7

# Static Files Serving
whitenoise==6.5.0

# Environment Variables
python-decouple==3.8

# Cloud Storage (AWS S3)
boto3==1.28.25
django-storages==1.13.2

# Caching and Task Queue
redis==4.6.0
celery==5.3.1

# Monitoring and Logging
sentry-sdk==1.29.2

# Security
django-cors-headers==4.2.0
