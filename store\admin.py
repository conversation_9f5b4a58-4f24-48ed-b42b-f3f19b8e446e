from django.contrib import admin
from .models import Product, Category, ReviewRating

class CategoryAdmin(admin.ModelAdmin):
    prepopulated_fields = {'slug': ('name',)}
    list_display = ('name', 'slug')
    search_fields = ['name', 'description']
    list_per_page = 20

class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'price', 'stock', 'category', 'is_available', 'created_date', 'modified_date')
    prepopulated_fields = {'slug': ('name',)}
    list_filter = ['category', 'is_available']
    search_fields = ['name', 'description']
    list_per_page = 20

class ReviewRatingAdmin(admin.ModelAdmin):
    list_display = ('product', 'user', 'subject', 'rating', 'status', 'created_at')
    list_filter = ['status', 'rating']
    search_fields = ['subject', 'review', 'user__email', 'product__name']
    list_per_page = 20

admin.site.register(Category, CategoryAdmin)
admin.site.register(Product, ProductAdmin)
admin.site.register(ReviewRating, ReviewRatingAdmin)
