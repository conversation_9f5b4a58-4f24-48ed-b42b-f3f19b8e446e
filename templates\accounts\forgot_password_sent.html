{% extends 'base.html' %}
{% load static %}

{% block title %}تم إرسال رابط إعادة التعيين | جريت كارت{% endblock %}

{% block content %}

<!-- ========================= بداية محتوى تأكيد الإرسال ========================= -->
<section class="section-content padding-y bg-light">
	<div class="container">
		<div class="row justify-content-center">
			<div class="col-lg-8 col-md-10">

				<!-- عرض الرسائل -->
				{% include 'includes/alerts.html' %}

				<!-- بطاقة تأكيد الإرسال -->
				<div class="card shadow-lg border-0">
					{% if reset_url %}
					<!-- في حالة فشل الإرسال -->
					<div class="card-header bg-warning text-white text-center">
						<h4 class="mb-0">
							<i class="fa fa-exclamation-triangle mr-2"></i>
							رابط إعادة التعيين
						</h4>
						<p class="mb-0 mt-2">تعذر إرسال البريد - استخدم الرابط أدناه</p>
					</div>
					<div class="card-body p-4 text-center">

						<!-- أيقونة التحذير -->
						<div class="mb-4">
							<i class="fa fa-exclamation-circle fa-5x text-warning mb-3"></i>
							<h5 class="text-warning">رابط مؤقت متاح</h5>
						</div>
					{% else %}
					<!-- في حالة نجاح الإرسال -->
					<div class="card-header bg-success text-white text-center">
						<h4 class="mb-0">
							<i class="fa fa-check-circle mr-2"></i>
							تم إرسال رابط إعادة التعيين
						</h4>
						<p class="mb-0 mt-2">تحقق من بريدك الإلكتروني</p>
					</div>
					<div class="card-body p-4 text-center">

						<!-- أيقونة النجاح -->
						<div class="mb-4">
							<i class="fa fa-envelope-open fa-5x text-success mb-3"></i>
							<h5 class="text-success">تم الإرسال بنجاح!</h5>
						</div>
					{% endif %}

						<!-- رسالة التأكيد -->
						{% if reset_url %}
						<!-- في حالة فشل الإرسال - عرض رابط مؤقت -->
						<div class="alert alert-warning">
							<h6><i class="fa fa-exclamation-triangle mr-2"></i>تعذر إرسال البريد الإلكتروني</h6>
							<p class="mb-3">
								لم نتمكن من إرسال البريد الإلكتروني إلى <strong>{{ reset_email }}</strong>.
								يمكنك استخدام الرابط المؤقت أدناه:
							</p>
							<div class="text-center">
								<a href="{{ reset_url }}" class="btn btn-warning btn-lg" target="_blank">
									<i class="fa fa-key mr-2"></i>
									إعادة تعيين كلمة المرور الآن
								</a>
							</div>
							<small class="text-muted d-block mt-2">
								<i class="fa fa-info-circle mr-1"></i>
								هذا الرابط صالح لمدة 24 ساعة فقط ويُستخدم مرة واحدة
							</small>
						</div>
						{% else %}
						<!-- في حالة نجاح الإرسال -->
						<div class="alert alert-success">
							<h6><i class="fa fa-info-circle mr-2"></i>ما الخطوة التالية؟</h6>
							<p class="mb-0">
								تم إرسال رابط إعادة تعيين كلمة المرور إلى <strong>{{ reset_email }}</strong>.
								يرجى التحقق من صندوق الوارد وصندوق الرسائل المزعجة.
							</p>
						</div>
						{% endif %}

						<!-- تعليمات للمستخدم -->
						<div class="row text-center mt-4">
							<div class="col-md-4">
								<div class="p-3">
									<i class="fa fa-envelope fa-2x text-primary mb-2"></i>
									<h6>1. تحقق من البريد</h6>
									<small class="text-muted">افتح بريدك الإلكتروني</small>
								</div>
							</div>
							<div class="col-md-4">
								<div class="p-3">
									<i class="fa fa-mouse-pointer fa-2x text-warning mb-2"></i>
									<h6>2. اضغط على الرابط</h6>
									<small class="text-muted">اضغط على زر إعادة التعيين</small>
								</div>
							</div>
							<div class="col-md-4">
								<div class="p-3">
									<i class="fa fa-key fa-2x text-success mb-2"></i>
									<h6>3. أدخل كلمة مرور جديدة</h6>
									<small class="text-muted">اختر كلمة مرور قوية</small>
								</div>
							</div>
						</div>

						<!-- معلومات مهمة -->
						<div class="alert alert-warning mt-4">
							<h6><i class="fa fa-exclamation-triangle mr-2"></i>ملاحظات مهمة:</h6>
							<ul class="text-right mb-0">
								<li>الرابط صالح لمدة 24 ساعة فقط</li>
								<li>يمكن استخدام الرابط مرة واحدة فقط</li>
								<li>إذا لم تجد الرسالة، تحقق من صندوق الرسائل المزعجة</li>
								<li>إذا لم تطلب إعادة تعيين كلمة المرور، تجاهل الرسالة</li>
							</ul>
						</div>

						<!-- أزرار العمل -->
						<div class="mt-4">
							<a href="{% url 'accounts:login' %}" class="btn btn-primary btn-lg mr-2">
								<i class="fa fa-sign-in mr-2"></i>
								تسجيل الدخول
							</a>
							<a href="{% url 'accounts:forgot_password' %}" class="btn btn-outline-secondary btn-lg">
								<i class="fa fa-redo mr-2"></i>
								إرسال مرة أخرى
							</a>
						</div>

						<!-- رابط الصفحة الرئيسية -->
						<div class="mt-3">
							<a href="{% url 'store:home' %}" class="text-muted">
								<i class="fa fa-home mr-2"></i>
								العودة للصفحة الرئيسية
							</a>
						</div>
					</div>
				</div>

				<!-- مساعدة إضافية -->
				<div class="card mt-4 border-0 bg-transparent">
					<div class="card-body text-center">
						<h6 class="text-muted mb-3">تحتاج مساعدة؟</h6>
						<div class="row">
							<div class="col-4">
								<i class="fa fa-envelope fa-2x text-primary mb-2"></i>
								<p class="small text-muted">راسلنا</p>
							</div>
							<div class="col-4">
								<i class="fa fa-phone fa-2x text-success mb-2"></i>
								<p class="small text-muted">اتصل بنا</p>
							</div>
							<div class="col-4">
								<i class="fa fa-comments fa-2x text-info mb-2"></i>
								<p class="small text-muted">دردشة مباشرة</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
<!-- ========================= نهاية محتوى تأكيد الإرسال ========================= -->

<style>
.card-header {
    border-radius: 10px 10px 0 0 !important;
}

.fa-5x {
    font-size: 4rem !important;
}

.alert ul {
    padding-right: 20px;
    text-align: right;
}

.btn-lg {
    border-radius: 8px;
    padding: 12px 24px;
}

.text-muted a:hover {
    text-decoration: none;
    color: #007bff !important;
}

.p-3 {
    transition: transform 0.2s ease;
}

.p-3:hover {
    transform: translateY(-2px);
}
</style>

{% endblock %}
