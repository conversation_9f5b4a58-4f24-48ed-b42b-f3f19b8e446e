{% extends 'base.html' %}
{% load static %}

{% block title %}سلة التسوق | جريت كارت{% endblock %}

{% block content %}

<section class="section-content padding-y bg">
<div class="container">

{% if not cart_items %}
	<h2 class="text-center">سلة التسوق فارغة</h2>
	<br>
	<div class="text-center">
		<a href="{% url 'store:store' %}" class="btn btn-primary">مواصلة التسوق</a>
	</div>
{% else %}
<div class="row">
	<aside class="col-lg-9">
<div class="card">
<table class="table table-borderless table-shopping-cart">
<thead class="text-muted">
<tr class="small text-uppercase">
  <th scope="col">المنتج</th>
  <th scope="col" width="120">الكمية</th>
  <th scope="col" width="120">السعر</th>
  <th scope="col" class="text-right" width="200"> </th>
</tr>
</thead>
<tbody>

{% for cart_item in cart_items %}
<tr>
	<td>
		<figure class="itemside align-items-center">
			<div class="aside"><img src="{{ cart_item.product.image.url }}" class="img-sm"></div>
			<figcaption class="info">
				<a href="{{ cart_item.product.get_url }}" class="title text-dark">{{ cart_item.product.name }}</a>
				<p class="text-muted small">
					{% if cart_item.variations.all %}
						{% for item in cart_item.variations.all %}
							{{ item.variation_category | capfirst }} : {{ item.variation_value | capfirst }} <br>
						{% endfor %}
					{% endif %}
				</p>
			</figcaption>
		</figure>
	</td>
	<td>
		<div class="col">
			<div class="input-group input-spinner">
				<div class="input-group-prepend">
					<a href="{% url 'cart:remove_cart' cart_item.product.id cart_item.id %}" class="btn btn-light" type="button"> <i class="fa fa-minus"></i> </a>
				</div>
				<input type="text" class="form-control" value="{{ cart_item.quantity }}" readonly>
				<div class="input-group-append">
					<a href="{% url 'cart:add_cart' cart_item.product.id %}?next=/cart/" class="btn btn-light" type="button"> <i class="fa fa-plus"></i> </a>
				</div>
			</div>
		</div>
	</td>
	<td>
		<div class="price-wrap">
			<var class="price">{{ cart_item.sub_total }} جنيه</var>
			<small class="text-muted"> {{ cart_item.product.price }} جنيه للوحدة </small>
		</div>
	</td>
	<td class="text-right">
	<a href="{% url 'cart:remove_cart_item' cart_item.product.id cart_item.id %}" onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')" class="btn btn-danger"> حذف</a>
	</td>
</tr>
{% endfor %}

</tbody>
</table>
</div>

	</aside>
	<aside class="col-lg-3">

		<div class="card">
		<div class="card-body">
			<dl class="dlist-align">
				<dt>السعر الإجمالي:</dt>
				<dd class="text-right">{{ total }} جنيه</dd>
			</dl>
			<dl class="dlist-align">
				<dt>الضريبة:</dt>
				<dd class="text-right"> {{ tax }} جنيه</dd>
			</dl>
			<dl class="dlist-align">
				<dt>المجموع الكلي:</dt>
				<dd class="text-right text-dark b"><strong>{{ grand_total }} جنيه</strong></dd>
			</dl>
			<hr>
			<p class="text-center mb-3">
				<img src="{% static './images/misc/payments.png' %}" height="26">
			</p>
			<a href="{% url 'orders:checkout' %}" class="btn btn-primary btn-block"> إتمام الطلب </a>
			<a href="{% url 'store:store' %}" class="btn btn-light btn-block">مواصلة التسوق</a>
		</div>
		</div>

	</aside>

</div>
{% endif %}

</div>
</section>

{% endblock %}