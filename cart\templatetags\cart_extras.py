from django import template
from cart.models import Cart, CartItem
from cart.views import _cart_id

register = template.Library()

@register.simple_tag(takes_context=True)
def cart_counter(context):
    """
    Template tag to get cart item count
    """
    request = context['request']
    cart_count = 0
    
    if 'admin' in request.path:
        return cart_count
    
    try:
        cart = Cart.objects.filter(cart_id=_cart_id(request))
        if request.user.is_authenticated:
            cart_items = CartItem.objects.all().filter(user=request.user)
        else:
            cart_items = CartItem.objects.all().filter(cart=cart[:1])
        for cart_item in cart_items:
            cart_count += cart_item.quantity
    except Cart.DoesNotExist:
        cart_count = 0
    
    return cart_count
