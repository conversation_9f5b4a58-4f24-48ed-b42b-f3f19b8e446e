{% extends 'base.html' %}
{% load static %}

{% block title %}تم إتمام الطلب | جريت كارت{% endblock %}

{% block content %}

<div class="container" style="margin-top: 50px;">
    <center><i class="fas fa-check-circle" style="font-size: 72px;margin-bottom: 20px;color: #28A745;"></i></center>
    <h2 class="text-center">تم الدفع بنجاح</h2>
	<br>
	<div class="text-center">
		<a href="{% url 'store:store' %}" class="btn btn-success">تسوق المزيد</a>
	</div>
</div>

<div class="container" style="margin: 0 auto;width: 50%;padding: 50px;background: #f1f1f1;margin-top: 50px;margin-bottom: 50px;">
    <div class="row invoice row-printable">
        <div class="col-md-12">
            <div class="panel panel-default plain" id="dash_0">
                <div class="panel-body p30">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="invoice-logo"><img src="{% static 'images/logo.png' %}" alt="شعار الفاتورة" style="max-height: 40px;"></div>
                        </div>
                        <div class="col-lg-6">
                            <div class="invoice-from">
                                <ul class="list-unstyled text-right">
                                    <li><strong>تم إرسال الفاتورة إلى</strong></li>
                                    <li>{{ order.full_name }}</li>
                                    <li>{{ order.full_address }}</li>
                                    <li>{{ order.city }}, {{ order.state }}</li>
                                    <li>{{ order.country }}</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="invoice-details mt25">
                                <div class="well">
                                    <ul class="list-unstyled mb0">
                                        <li><strong>رقم الطلب:</strong> #{{ order.order_number }}</li>
                                        <li><strong>رقم المعاملة:</strong> {{ payment.payment_id }}</li>
                                        <li><strong>تاريخ الطلب:</strong> {{ order.created_at }}</li>
                                        <li><strong>الحالة:</strong> تم الدفع</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="invoice-items">
                                <div class="table-responsive" style="overflow: hidden; outline: none;" tabindex="0">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th class="per70 text-center">الوصف</th>
                                                <th class="per5 text-center">الكمية</th>
                                                <th class="per25 text-center">المجموع</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        {% for item in order_detail %}
                                            <tr>
                                                <td>{{ item.product.product_name }}</td>
                                                <td class="text-center">{{ item.quantity }}</td>
                                                <td class="text-center">{{ item.product_price }} جنيه</td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="2" class="text-right">المجموع الفرعي:</th>
                                                <th class="text-center">{{ subtotal }} جنيه</th>
                                            </tr>
                                            <tr>
                                                <th colspan="2" class="text-right">الضريبة:</th>
                                                <th class="text-center">{{ order.tax }} جنيه</th>
                                            </tr>
                                            <tr>
                                                <th colspan="2" class="text-right">المجموع الكلي:</th>
                                                <th class="text-center">{{ order.order_total }} جنيه</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                            <div class="invoice-footer mt25">
                                <p class="text-center">شكراً لتسوقك معنا!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
