# Django GreatKart Arabic E-commerce Project Environment Variables
# Copy this file to .env and update the values

# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration (SQLite by default)
# For PostgreSQL, uncomment and configure:
# DATABASE_URL=postgresql://username:password@localhost:5432/greatkart_db

# Email Configuration
# للإرسال الفعلي، قم بتعبئة البيانات التالية:
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=جريت كارت <<EMAIL>>

# إعدادات البريد الإلكتروني
# للاختبار: file (حفظ في ملف)
# للإنتاج: smtp (إرسال فعلي)
EMAIL_BACKEND=smtp
EMAIL_TIMEOUT=30

# إعدادات بديلة لخدمات البريد الأخرى:
# Outlook/Hotmail
# EMAIL_HOST=smtp-mail.outlook.com
# EMAIL_PORT=587

# Yahoo
# EMAIL_HOST=smtp.mail.yahoo.com
# EMAIL_PORT=587

# Static and Media Files
STATIC_URL=/static/
MEDIA_URL=/media/

# AWS S3 Configuration (for production)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_STORAGE_BUCKET_NAME=your-bucket-name
# AWS_S3_REGION_NAME=us-east-1

# Payment Gateway Configuration
# STRIPE_PUBLIC_KEY=pk_test_...
# STRIPE_SECRET_KEY=sk_test_...
# PAYPAL_CLIENT_ID=your-paypal-client-id
# PAYPAL_CLIENT_SECRET=your-paypal-client-secret

# Cache Configuration (Redis)
# REDIS_URL=redis://localhost:6379/1

# Security Settings (for production)
# SECURE_SSL_REDIRECT=True
# SECURE_HSTS_SECONDS=31536000
# SECURE_HSTS_INCLUDE_SUBDOMAINS=True
# SECURE_HSTS_PRELOAD=True
