// ملف ترجمة النصوص من الإنجليزية إلى العربية

// ترجمة الرسائل والنصوص الديناميكية
const arabicTranslations = {
    // رسائل التسجيل وتسجيل الدخول
    'Registration successful': 'تم التسجيل بنجاح',
    'Login successful': 'تم تسجيل الدخول بنجاح',
    'Invalid login credentials': 'بيانات تسجيل الدخول غير صحيحة',
    'Please activate your account': 'الرجاء تفعيل حسابك',
    'Password reset email sent': 'تم إرسال رابط إعادة تعيين كلمة المرور',
    'Account activated successfully': 'تم تفعيل الحساب بنجاح',
    'Invalid activation link': 'رابط التفعيل غير صالح',

    // رسائل السلة
    'Product added to cart': 'تمت إضافة المنتج إلى السلة',
    'Product removed from cart': 'تمت إزالة المنتج من السلة',
    'Cart updated successfully': 'تم تحديث السلة بنجاح',
    'Cart is empty': 'السلة فارغة',

    // رسائل الطلبات
    'Order placed successfully': 'تم تقديم الطلب بنجاح',
    'Payment successful': 'تم الدفع بنجاح',
    'Payment failed': 'فشلت عملية الدفع',
    'Order cancelled': 'تم إلغاء الطلب',

    // رسائل التقييمات
    'Review submitted successfully': 'تم إرسال التقييم بنجاح',
    'You have already reviewed this product': 'لقد قمت بتقييم هذا المنتج مسبقاً',
    'Please login to submit review': 'الرجاء تسجيل الدخول لإرسال تقييم',

    // رسائل عامة
    'Success': 'نجاح',
    'Error': 'خطأ',
    'Warning': 'تحذير',
    'Info': 'معلومات',
    'Loading': 'جاري التحميل',
    'Please wait': 'الرجاء الانتظار',
    'No results found': 'لم يتم العثور على نتائج',
    'Required field': 'حقل مطلوب',
    'Invalid input': 'مدخلات غير صحيحة',
    'Are you sure?': 'هل أنت متأكد؟',
    'Yes': 'نعم',
    'No': 'لا',
    'Cancel': 'إلغاء',
    'Save': 'حفظ',
    'Delete': 'حذف',
    'Edit': 'تعديل',
    'Update': 'تحديث',
    'Close': 'إغلاق',

    // ترجمة عناصر القائمة العلوية
    'English': 'العربية',
    'USD': 'ج.م',
    'Email': 'البريد الإلكتروني',
    'Call us': 'اتصل بنا',
    
    // ترجمة عناصر رأس الصفحة
    'Welcome guest!': 'مرحباً بالزائر!',
    'Sign in': 'تسجيل الدخول',
    'Register': 'التسجيل',
    'All category': 'جميع الفئات',
    'Store': 'المتجر',
    'Search': 'بحث',
    
    // ترجمة فئات المنتجات
    'Machinery / Mechanical Parts / Tools': 'الآلات / قطع ميكانيكية / أدوات',
    'Consumer Electronics / Home Appliances': 'الإلكترونيات الاستهلاكية / الأجهزة المنزلية',
    'Auto / Transportation': 'السيارات / النقل',
    'Apparel / Textiles / Timepieces': 'الملابس / المنسوجات / الساعات',
    'Home & Garden / Construction / Lights': 'المنزل والحديقة / البناء / الإضاءة',
    'Beauty & Personal Care / Health': 'الجمال والعناية الشخصية / الصحة',
    
    // ترجمة عناصر الصفحة الرئيسية
    'Popular products': 'المنتجات الشائعة',
    'See all': 'عرض الكل',
    'Just another product name': 'اسم منتج آخر',
    'Some item name here': 'اسم عنصر هنا',
    'Great product name here': 'اسم منتج رائع هنا',
    'Add to cart': 'أضف إلى السلة',
    
    // ترجمة عناصر تذييل الصفحة
    'Payment': 'الدفع',
    'About us': 'من نحن',
    'Partnership': 'شراكة',
    'Help center': 'مركز المساعدة',
    'User information': 'معلومات المستخدم',
    'How to buy': 'كيفية الشراء',
    'Contact us': 'اتصل بنا',
    'Phones': 'الهواتف',
    'Email': 'البريد الإلكتروني',
    'Location': 'الموقع',
    'Orders': 'الطلبات',
    'File a claim': 'تقديم مطالبة',
    'Payments': 'المدفوعات',
    'Shipping': 'الشحن',
    'Returns': 'المرتجعات',
    'Social media': 'وسائل التواصل الاجتماعي',
    'Facebook': 'فيسبوك',
    'Twitter': 'تويتر',
    'Instagram': 'انستغرام',
    'Youtube': 'يوتيوب',
    'All rights reserved': 'جميع الحقوق محفوظة',
    
    // ترجمة صفحة المنتج
    'Description': 'الوصف',
    'Specifications': 'المواصفات',
    'Customer Reviews': 'آراء العملاء',
    'Similar items': 'عناصر مشابهة',
    'Add to wishlist': 'أضف إلى قائمة الرغبات',
    'Share': 'مشاركة',
    'Quantity': 'الكمية',
    'Buy now': 'اشتر الآن',
    
    // ترجمة صفحة السلة
    'Shopping cart': 'عربة التسوق',
    'Remove': 'إزالة',
    'Coupon code': 'رمز القسيمة',
    'Apply': 'تطبيق',
    'Total price': 'السعر الإجمالي',
    'Discount': 'خصم',
    'VAT': 'ضريبة القيمة المضافة',
    'Checkout': 'الدفع',
    'Continue shopping': 'مواصلة التسوق',
    
    // ترجمة صفحة تسجيل الدخول
    'Username': 'اسم المستخدم',
    'Password': 'كلمة المرور',
    'Remember': 'تذكرني',
    'Forgot password': 'نسيت كلمة المرور',
    'Login': 'تسجيل الدخول',
    'Or sign in with': 'أو سجل الدخول باستخدام',
    'Don\'t have an account?': 'ليس لديك حساب؟',
    
    // ترجمة صفحة التسجيل
    'First name': 'الاسم الأول',
    'Last name': 'الاسم الأخير',
    'Email address': 'البريد الإلكتروني',
    'Confirm password': 'تأكيد كلمة المرور',
    'I agree with terms and conditions': 'أوافق على الشروط والأحكام',
    'Create Account': 'إنشاء حساب',
    'Already have an account?': 'لديك حساب بالفعل؟',
    
    // ترجمة صفحة الدفع
    'Billing details': 'تفاصيل الفواتير',
    'Shipping address': 'عنوان الشحن',
    'Payment method': 'طريقة الدفع',
    'Credit Card': 'بطاقة ائتمان',
    'PayPal': 'باي بال',
    'Bank Transfer': 'تحويل بنكي',
    'Order summary': 'ملخص الطلب',
    'Place order': 'تقديم الطلب',
    
    // ترجمة صفحة اكتمال الطلب
    'Order Complete': 'اكتمل الطلب',
    'Thank you for your order!': 'شكرا لطلبك!',
    'Your order number is': 'رقم طلبك هو',
    'You will receive an order confirmation email with details of your order.': 'ستتلقى رسالة بريد إلكتروني لتأكيد الطلب مع تفاصيل طلبك.',
    'Continue to shop': 'الاستمرار في التسوق',
    
    // ترجمة صفحة البحث
    'Search results for': 'نتائج البحث عن',
    'No results found': 'لم يتم العثور على نتائج',
    'Filter': 'تصفية',
    'Sort by': 'ترتيب حسب',
    'Price low to high': 'السعر من الأقل إلى الأعلى',
    'Price high to low': 'السعر من الأعلى إلى الأقل',
    'Newest': 'الأحدث',
    'Popular': 'الأكثر شعبية',

    // General
    'Welcome': 'مرحباً',
    'Search': 'بحث',
    'Categories': 'الفئات',
    'All Categories': 'جميع الفئات',
    'Cart': 'سلة التسوق',
    'Profile': 'الملف الشخصي',
    'Logout': 'تسجيل الخروج',
    'Login': 'تسجيل الدخول',
    'Register': 'تسجيل جديد',
    'My Account': 'حسابي',
    'Dashboard': 'لوحة التحكم',

    // Product related
    'Add to Cart': 'أضف إلى السلة',
    'Remove': 'حذف',
    'Price': 'السعر',
    'Quantity': 'الكمية',
    'Total': 'الإجمالي',
    'Subtotal': 'المجموع الفرعي',
    'Continue Shopping': 'مواصلة التسوق',
    'Checkout': 'إتمام الشراء',
    'Product Description': 'وصف المنتج',
    'Related Products': 'منتجات ذات صلة',
    'Out of Stock': 'نفذت الكمية',
    'In Stock': 'متوفر',

    // Shopping Cart
    'Shopping Cart': 'سلة التسوق',
    'Update': 'تحديث',
    'Clear Cart': 'تفريغ السلة',
    'Cart Total': 'إجمالي السلة',
    'Tax': 'الضريبة',
    'Grand Total': 'المجموع الكلي',

    // Checkout
    'Billing Details': 'تفاصيل الفاتورة',
    'First Name': 'الاسم الأول',
    'Last Name': 'اسم العائلة',
    'Email': 'البريد الإلكتروني',
    'Phone': 'رقم الهاتف',
    'Address': 'العنوان',
    'City': 'المدينة',
    'State': 'المحافظة',
    'Country': 'الدولة',
    'Order Notes': 'ملاحظات الطلب',
    'Place Order': 'تأكيد الطلب',

    // Order
    'Order Complete': 'تم الطلب بنجاح',
    'Order Number': 'رقم الطلب',
    'Order Date': 'تاريخ الطلب',
    'Order Status': 'حالة الطلب',
    'Payment Method': 'طريقة الدفع',
    'Shipping Method': 'طريقة الشحن',
};

// دالة الترجمة
function translate(text) {
    return arabicTranslations[text] || text;
}

// دالة لترجمة النص
function translateToArabic(text) {
    return arabicTranslations[text] || text;
}

// دالة تحويل الأرقام إلى العربية
function convertToArabicNumbers(str) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return str.replace(/[0-9]/g, w => arabicNumbers[w]);
}

// دالة لترجمة جميع النصوص في الصفحة
function translatePage() {
    // ترجمة النصوص العادية
    document.querySelectorAll('a, button, h1, h2, h3, h4, h5, h6, p, span, label, option, th, td').forEach(element => {
        if (element.childNodes.length === 1 && element.childNodes[0].nodeType === 3) {
            let text = element.textContent.trim();
            // ترجمة النص أولاً
            text = translateToArabic(text);
            // ثم تحويل الأرقام إذا كانت موجودة
            if (/[0-9]/.test(text)) {
                text = convertToArabicNumbers(text);
            }
            element.textContent = text;
        }
    });

    // ترجمة الأسعار والأرقام في عناصر معينة
    document.querySelectorAll('.price, .quantity, .number').forEach(element => {
        let text = element.textContent;
        if (/[0-9]/.test(text)) {
            element.textContent = convertToArabicNumbers(text);
        }
    });

    // ترجمة العناصر التي تحتوي على سمة placeholder
    document.querySelectorAll('input[placeholder], textarea[placeholder]').forEach(element => {
        const originalPlaceholder = element.getAttribute('placeholder');
        const translatedPlaceholder = translateToArabic(originalPlaceholder);
        if (translatedPlaceholder !== originalPlaceholder) {
            element.setAttribute('placeholder', translatedPlaceholder);
        }
    });
    
    // ترجمة العناصر التي تحتوي على سمة title
    document.querySelectorAll('[title]').forEach(element => {
        const originalTitle = element.getAttribute('title');
        const translatedTitle = translateToArabic(originalTitle);
        if (translatedTitle !== originalTitle) {
            element.setAttribute('title', translatedTitle);
        }
    });
}

// تحديث الأرقام في سلة التسوق بشكل ديناميكي
function updateCartNumbers() {
    document.querySelectorAll('.cart-quantity, .cart-price, .cart-total').forEach(element => {
        let text = element.textContent;
        if (/[0-9]/.test(text)) {
            element.textContent = convertToArabicNumbers(text);
        }
    });
}

// إضافة مراقب لتحديثات السلة
document.addEventListener('cart:updated', updateCartNumbers);

// تنفيذ الترجمة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    translatePage();
    updateCartNumbers();
});