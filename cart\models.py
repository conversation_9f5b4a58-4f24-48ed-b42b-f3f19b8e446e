from django.db import models
from store.models import Product
from accounts.models import Account

class Cart(models.Model):
    cart_id = models.CharField('معرف السلة', max_length=250, blank=True)
    date_added = models.DateField('تاريخ الإضافة', auto_now_add=True)

    class Meta:
        verbose_name = 'سلة'
        verbose_name_plural = 'السلات'

    def __str__(self):
        return self.cart_id

class CartItem(models.Model):
    user = models.ForeignKey(Account, verbose_name='المستخدم', on_delete=models.CASCADE, null=True)
    product = models.ForeignKey(Product, verbose_name='المنتج', on_delete=models.CASCADE)
    cart = models.ForeignKey(Cart, verbose_name='السلة', on_delete=models.CASCADE, null=True)
    quantity = models.IntegerField('الكمية')
    is_active = models.BooleanField('نشط', default=True)

    class Meta:
        verbose_name = 'عنصر السلة'
        verbose_name_plural = 'عناصر السلة'

    def sub_total(self):
        return self.product.price * self.quantity

    def __str__(self):
        return self.product.name
