from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import Account, UserProfile

class AccountAdmin(UserAdmin):
    list_display = ('email', 'first_name', 'last_name', 'username', 'last_login', 'date_joined', 'is_active')
    list_display_links = ('email', 'first_name', 'last_name')
    readonly_fields = ('last_login', 'date_joined')
    ordering = ('-date_joined',)
    list_filter = ('is_active', 'is_staff', 'is_admin')
    search_fields = ('email', 'first_name', 'last_name', 'username')
    list_per_page = 20
    
    filter_horizontal = ()
    fieldsets = (
        ('معلومات المستخدم', {
            'fields': ('email', 'password')
        }),
        ('المعلومات الشخصية', {
            'fields': ('first_name', 'last_name', 'username', 'phone_number')
        }),
        ('الصلاحيات', {
            'fields': ('is_active', 'is_staff', 'is_admin', 'is_superadmin')
        }),
        ('تواريخ مهمة', {
            'fields': ('last_login', 'date_joined')
        })
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2', 'first_name', 'last_name', 'username'),
        }),
    )

class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'city', 'full_address')
    search_fields = ('user__email', 'user__first_name', 'city')
    list_filter = ('city',)
    list_per_page = 20

admin.site.register(Account, AccountAdmin)
admin.site.register(UserProfile, UserProfileAdmin)
