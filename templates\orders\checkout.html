{% extends 'base.html' %}
{% load static %}

{% block title %}إتمام الطلب | جريت كارت{% endblock %}

{% block content %}
<!-- ========================= SECTION CONTENT ========================= -->
<section class="section-content padding-y bg">
<div class="container">

<!-- ============================ COMPONENT 1 ================================= -->
<div class="row">
	<aside class="col-lg-6">
<div class="card">
<div class="card-body">
	<h4 class="card-title mb-4">عنوان التوصيل</h4>
	<form action="{% url 'orders:place_order' %}" method="POST">
		{% csrf_token %}
		<div class="form-row">
			<div class="col form-group">
				<label>الاسم الأول</label>
			  	<input type="text" name="first_name" class="form-control" required>
			</div>
			<div class="col form-group">
				<label>الاسم الأخير</label>
			  	<input type="text" name="last_name" class="form-control" required>
			</div>
		</div> <!-- form-row.// -->
		<div class="form-row">
			<div class="col form-group">
				<label>البريد الإلكتروني</label>
			  	<input type="email" name="email" class="form-control" required>
			</div>
			<div class="col form-group">
				<label>رقم الهاتف</label>
			  	<input type="tel" name="phone" class="form-control"
			  	       pattern="[0-9+\-\s\(\)]+"
			  	       placeholder="مثال: 01234567890 أو +201234567890"
			  	       title="يرجى إدخال أرقام فقط (يمكن أن يحتوي على + في البداية)"
			  	       required>
			  	<small class="form-text text-muted">يرجى إدخال رقم هاتف صحيح (10-15 رقم)</small>
			</div>
		</div> <!-- form-row.// -->
		<div class="form-row">
			<div class="col form-group">
				<label>العنوان الأول</label>
			  	<input type="text" name="address_line_1" class="form-control" required>
			</div>
			<div class="col form-group">
				<label>العنوان الثاني</label>
			  	<input type="text" name="address_line_2" class="form-control">
			</div>
		</div> <!-- form-row.// -->
		<div class="form-row">
			<div class="col form-group">
				<label>المدينة</label>
			  	<input type="text" name="city" class="form-control" required>
			</div>
		</div> <!-- form-row.// -->
		<div class="form-row">
			<div class="col form-group">
				<label>ملاحظات الطلب</label>
			  	<textarea name="order_note" rows="2" class="form-control"></textarea>
			</div>
		</div> <!-- form-row.// -->

</div> <!-- card-body.// -->
</div> <!-- card.// -->

	</aside> <!-- col.// -->
	<aside class="col-lg-6">

		<div class="card">
		<div class="card-body">
			<dl class="dlist-align">
			  <dt>المجموع الفرعي:</dt>
			  <dd class="text-right">{{ total }} جنيه</dd>
			</dl>
			<dl class="dlist-align">
			  <dt>الضرائب:</dt>
			  <dd class="text-right"> {{ tax }} جنيه</dd>
			</dl>
			<dl class="dlist-align">
			  <dt>المجموع الإجمالي:</dt>
			  <dd class="text-right text-dark b"><strong>{{ grand_total }} جنيه</strong></dd>
			</dl>
			<hr>
			<p class="text-center mb-3">
				<img src="{% static './images/misc/payments.png' %}" height="26">
			</p>
			<button type="submit" class="btn btn-primary btn-block"> إتمام الطلب </button>
			<a href="{% url 'cart:cart' %}" class="btn btn-light btn-block">العودة للسلة</a>
		</div> <!-- card-body.// -->
		</div> <!-- card.// -->

</aside> <!-- col.// -->


</div> <!-- row.// -->

<!-- ============================ COMPONENT 1 END .// ================================= -->

</div> <!-- container .//  -->
</section>
<!-- ========================= SECTION CONTENT END// ========================= -->

<script>
// التحقق من صحة رقم الهاتف في الوقت الفعلي
document.addEventListener('DOMContentLoaded', function() {
    const phoneInput = document.querySelector('input[name="phone"]');
    const phoneHelp = phoneInput.nextElementSibling;

    phoneInput.addEventListener('input', function() {
        let value = this.value;

        // إزالة أي أحرف غير مسموحة
        value = value.replace(/[^0-9+\-\s\(\)]/g, '');
        this.value = value;

        // التحقق من صحة الرقم
        const digitsOnly = value.replace(/[^0-9]/g, '');

        if (value === '') {
            phoneHelp.textContent = 'يرجى إدخال رقم هاتف صحيح (10-15 رقم)';
            phoneHelp.className = 'form-text text-muted';
            this.setCustomValidity('');
        } else if (digitsOnly.length < 10) {
            phoneHelp.textContent = 'رقم الهاتف قصير جداً (يجب أن يكون 10 أرقام على الأقل)';
            phoneHelp.className = 'form-text text-danger';
            this.setCustomValidity('رقم الهاتف قصير جداً');
        } else if (digitsOnly.length > 15) {
            phoneHelp.textContent = 'رقم الهاتف طويل جداً (يجب أن يكون 15 رقم كحد أقصى)';
            phoneHelp.className = 'form-text text-danger';
            this.setCustomValidity('رقم الهاتف طويل جداً');
        } else {
            phoneHelp.textContent = '✓ رقم هاتف صحيح';
            phoneHelp.className = 'form-text text-success';
            this.setCustomValidity('');
        }
    });

    // منع إدخال أحرف غير مسموحة
    phoneInput.addEventListener('keypress', function(e) {
        const allowedChars = /[0-9+\-\s\(\)]/;
        if (!allowedChars.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
            e.preventDefault();
        }
    });
});
</script>

{% endblock %}