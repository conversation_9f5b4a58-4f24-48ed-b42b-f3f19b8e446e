{% extends 'base.html' %}
{% load static %}

{% block title %}الدفع | جريت كارت{% endblock %}

{% block content %}
<!-- ========================= SECTION CONTENT ========================= -->
<section class="section-content padding-y bg">
<div class="container">

<!-- ============================ COMPONENT 1 ================================= -->
<div class="row">
	<aside class="col-lg-8">
<div class="card">
<div class="card-body">
	<h4 class="card-title mb-4">مراجعة الطلب</h4>

	<!-- عرض منتجات الطلب -->
	<div class="row">
		{% for cart_item in cart_items %}
		<div class="col-md-12">
			<figure class="itemside mb-3">
				<div class="aside">
					{% if cart_item.product.image %}
						<img src="{{ cart_item.product.image.url }}" class="img-sm border">
					{% else %}
						<img src="{% static 'images/default-product.png' %}" class="img-sm border">
					{% endif %}
				</div>
				<figcaption class="info">
					<a href="{{ cart_item.product.get_url }}" class="title text-dark">{{ cart_item.product.product_name }}</a>
					<p class="text-muted small">
						{% if cart_item.variations.all %}
							{% for item in cart_item.variations.all %}
								{{ item.variation_category | capfirst }} : {{ item.variation_value | capfirst }} <br>
							{% endfor %}
						{% endif %}
					</p>
				</figcaption>
			</figure>
		</div>
		<div class="col-md-4">
			<label>الكمية:</label>
			<span class="text-muted">{{ cart_item.quantity }}</span>
		</div>
		<div class="col-md-4">
			<label>سعر الوحدة:</label>
			<span class="text-muted">{{ cart_item.product.price }} جنيه</span>
		</div>
		<div class="col-md-4">
			<label>المجموع:</label>
			<span class="text-muted">{{ cart_item.sub_total }} جنيه</span>
		</div>
		<hr>
		{% endfor %}
	</div>

</div> <!-- card-body.// -->
</div> <!-- card.// -->

	</aside> <!-- col.// -->
	<aside class="col-lg-4">

		<div class="card">
		<div class="card-body">
			<dl class="dlist-align">
			  <dt>المجموع الفرعي:</dt>
			  <dd class="text-right">{{ total }} جنيه</dd>
			</dl>
			<dl class="dlist-align">
			  <dt>الضرائب:</dt>
			  <dd class="text-right"> {{ tax }} جنيه</dd>
			</dl>
			<dl class="dlist-align">
			  <dt>المجموع الإجمالي:</dt>
			  <dd class="text-right text-dark b"><strong>{{ grand_total }} جنيه</strong></dd>
			</dl>
			<hr>

			<!-- معلومات الطلب -->
			<h5 class="card-title">معلومات الطلب</h5>
			<p><strong>رقم الطلب:</strong> {{ order.order_number }}</p>
			<p><strong>الاسم:</strong> {{ order.first_name }} {{ order.last_name }}</p>
			<p><strong>الهاتف:</strong> {{ order.phone }}</p>
			<p><strong>البريد الإلكتروني:</strong> {{ order.email }}</p>
			<p><strong>العنوان:</strong> {{ order.address_line_1 }}
				{% if order.address_line_2 %}, {{ order.address_line_2 }}{% endif %}
			</p>
			<p><strong>المدينة:</strong> {{ order.city }}</p>
			{% if order.order_note %}
			<p><strong>ملاحظات:</strong> {{ order.order_note }}</p>
			{% endif %}

			<hr>

			<!-- خيارات الدفع -->
			<h5 class="card-title">طريقة الدفع</h5>
			<div class="payment-methods">
				<div class="form-check mb-3">
					<input class="form-check-input" type="radio" name="payment_method" id="cash" value="الدفع عند الاستلام" checked>
					<label class="form-check-label" for="cash">
						<span style="margin-right: 10px;">💰</span> الدفع عند الاستلام
					</label>
				</div>
				<div class="form-check mb-3">
					<input class="form-check-input" type="radio" name="payment_method" id="paypal" value="PayPal">
					<label class="form-check-label" for="paypal">
						<img src="{% static 'images/misc/payment-paypal.png' %}" height="20" style="margin-left: 10px; margin-right: 5px;"> PayPal
					</label>
				</div>
			</div>

			<hr>

			<!-- Cash on Delivery Button -->
			<div id="cash-button" style="display: block;">
				{% csrf_token %}
				<button type="button" class="btn btn-success btn-block" onclick="confirmCashOrder()">
					تأكيد الطلب - الدفع عند الاستلام
				</button>
			</div>

			<!-- PayPal Button -->
			<div id="paypal-button-container" style="display: none;"></div>

			<a href="{% url 'orders:checkout' %}" class="btn btn-light btn-block mt-2">العودة للخطوة السابقة</a>
		</div> <!-- card-body.// -->
		</div> <!-- card.// -->

</aside> <!-- col.// -->

</div> <!-- row.// -->

<!-- ============================ COMPONENT 1 END .// ================================= -->

</div> <!-- container .//  -->
</section>
<!-- ========================= SECTION CONTENT END// ========================= -->

<!-- PayPal SDK -->
<script src="https://www.paypal.com/sdk/js?client-id=test&currency=USD"></script>

<script>
// إظهار/إخفاء أزرار الدفع حسب الطريقة المختارة
document.querySelectorAll('input[name="payment_method"]').forEach(function(radio) {
    radio.addEventListener('change', function() {
        if (this.value === 'الدفع عند الاستلام') {
            document.getElementById('cash-button').style.display = 'block';
            document.getElementById('paypal-button-container').style.display = 'none';
        } else {
            document.getElementById('cash-button').style.display = 'none';
            document.getElementById('paypal-button-container').style.display = 'block';
        }
    });
});

// PayPal Integration
paypal.Buttons({
    createOrder: function(data, actions) {
        return actions.order.create({
            purchase_units: [{
                amount: {
                    value: '{{ grand_total }}'
                }
            }]
        });
    },
    onApprove: function(data, actions) {
        return actions.order.capture().then(function(details) {
            // إرسال تفاصيل الدفع للخادم
            sendData(details, '{{ order.order_number }}');
        });
    }
}).render('#paypal-button-container');

// إرسال بيانات الدفع
function sendData(details, order_number) {
    var url = "{% url 'orders:payments' %}";

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}',
        },
        body: JSON.stringify({
            orderID: order_number,
            transID: details.id,
            payment_method: 'PayPal',
            status: details.status,
        })
    })
    .then(response => response.json())
    .then(data => {
        window.location.href = "{% url 'orders:order_complete' %}?order_number=" + data.order_number + "&payment_id=" + data.transID;
    });
}

// تأكيد الطلب للدفع عند الاستلام
function confirmCashOrder() {
    // إظهار loading
    const button = document.querySelector('#cash-button button');
    const originalText = button.textContent;
    button.textContent = 'جاري معالجة الطلب...';
    button.disabled = true;

    var url = "{% url 'orders:payments' %}";

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
        body: JSON.stringify({
            orderID: '{{ order.order_number }}',
            transID: 'CASH_' + Date.now(),
            payment_method: 'الدفع عند الاستلام',
            status: 'COMPLETED',
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        console.log('Success:', data);
        // توجيه المستخدم للصفحة المناسبة حسب طريقة الدفع
        if (data.is_cash_payment) {
            window.location.href = "{% url 'orders:order_confirmed' %}?order_number=" + data.order_number + "&payment_id=" + data.transID;
        } else {
            window.location.href = "{% url 'orders:order_complete' %}?order_number=" + data.order_number + "&payment_id=" + data.transID;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى.');
        // إعادة تفعيل الزر
        button.textContent = originalText;
        button.disabled = false;
    });
}
</script>

{% endblock %}
