{% extends 'base.html' %}
{% load static %}

{% block title %}جريت كارت | الصفحة الرئيسية{% endblock %}

{% block content %}
<!-- ========================= SECTION MAIN ========================= -->
<section class="section-intro padding-y-sm">
<div class="container">

<div class="intro-banner-wrap">
    <div class="text-center">
        <h1 class="mb-4">مرحباً بكم في جريت كارت</h1>
        <p class="lead">متجرك الإلكتروني المفضل</p>
    </div>
</div>

</div> <!-- container //  -->
</section>
<!-- ========================= SECTION MAIN END// ========================= -->

<!-- ========================= SECTION SERVICES ========================= -->
{% if user.id is not None %}
<section class="section-content padding-y-sm bg-light">
<div class="container">
    <div class="row">
        <div class="col-md-4 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <i class="fa fa-list-alt fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">طلباتي</h5>
                    <p class="card-text">عرض وتتبع جميع طلباتك</p>
                    <a href="{% url 'orders:my_orders' %}" class="btn btn-primary">عرض الطلبات</a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <i class="fa fa-search fa-3x text-success mb-3"></i>
                    <h5 class="card-title">تتبع الطلب</h5>
                    <p class="card-text">تتبع حالة طلبك بالتفصيل</p>
                    <a href="{% url 'orders:track_order' %}" class="btn btn-success">تتبع الطلب</a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <i class="fa fa-user fa-3x text-info mb-3"></i>
                    <h5 class="card-title">حسابي</h5>
                    <p class="card-text">إدارة معلومات حسابك</p>
                    <a href="{% url 'accounts:dashboard' %}" class="btn btn-info">لوحة التحكم</a>
                </div>
            </div>
        </div>
    </div>
</div>
</section>
{% endif %}
<!-- ========================= SECTION SERVICES END// ========================= -->

<!-- ========================= SECTION PRODUCTS ========================= -->
<section class="section-content padding-y">
<div class="container">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">المنتجات المتاحة</h2>
        </div>
    </div>

    <div class="row">
        {% if products %}
            {% for product in products %}
            <div class="col-md-4 mb-4">
                <div class="card">
                    {% if product.image %}
                    <img src="{{ product.image.url }}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                    {% endif %}
                    <div class="card-body">
                        <h5 class="card-title">{{ product.name }}</h5>
                        <p class="card-text">{{ product.description|truncatewords:10 }}</p>
                        <p class="text-primary fw-bold">{{ product.price }} جنيه</p>
                        <a href="{% url 'store:product_detail' product.category.slug product.slug %}" class="btn btn-primary">عرض التفاصيل</a>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <h4>لا توجد منتجات متاحة حالياً</h4>
                    <p>يرجى المحاولة لاحقاً</p>
                </div>
            </div>
        {% endif %}
    </div>

    <div class="text-center mt-5">
        <a href="{% url 'store:store' %}" class="btn btn-success btn-lg">تصفح جميع المنتجات</a>
    </div>

</div> <!-- container //  -->
</section>
<!-- ========================= SECTION PRODUCTS END// ========================= -->
{% endblock %}
