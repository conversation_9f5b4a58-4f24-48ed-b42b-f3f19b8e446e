{% extends 'base.html' %}
{% load static %}

{% block content %}

<!-- ========================= محتوى التسجيل ========================= -->
<section class="section-content padding-y bg-light">
	<div class="container">
		<div class="row justify-content-center">
			<div class="col-md-6 col-lg-5">
				<!-- رسائل النجاح والخطأ -->
				{% if messages %}
				<div class="mb-4">
					{% for message in messages %}
					<div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
						<i class="fa fa-info-circle mr-2"></i>
						{{ message }}
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					{% endfor %}
				</div>
				{% endif %}

				<!-- بطاقة التسجيل -->
				<div class="card shadow-lg border-0">
					<div class="card-header bg-primary text-white text-center">
						<h4 class="mb-0">
							<i class="fa fa-user-plus mr-2"></i>
							إنشاء حساب جديد
						</h4>
						<p class="mb-0 mt-2">انضم إلينا واستمتع بتجربة تسوق رائعة</p>
					</div>
					<div class="card-body p-4">
						<form method="post" class="register-form">
							{% csrf_token %}

							<!-- الاسم الأول والأخير -->
							<div class="row">
								<div class="col-md-6 mb-3">
									<label for="first_name" class="form-label">
										<i class="fa fa-user mr-2"></i>
										الاسم الأول <span class="text-danger">*</span>
									</label>
									<input type="text"
										   class="form-control"
										   id="first_name"
										   name="first_name"
										   value="{{ form.first_name.value|default:'' }}"
										   required
										   placeholder="أدخل الاسم الأول">
									{% if form.first_name.errors %}
									<div class="text-danger small mt-1">
										{{ form.first_name.errors.0 }}
									</div>
									{% endif %}
								</div>
								<div class="col-md-6 mb-3">
									<label for="last_name" class="form-label">
										<i class="fa fa-user mr-2"></i>
										الاسم الأخير <span class="text-danger">*</span>
									</label>
									<input type="text"
										   class="form-control"
										   id="last_name"
										   name="last_name"
										   value="{{ form.last_name.value|default:'' }}"
										   required
										   placeholder="أدخل الاسم الأخير">
									{% if form.last_name.errors %}
									<div class="text-danger small mt-1">
										{{ form.last_name.errors.0 }}
									</div>
									{% endif %}
								</div>
							</div>

							<!-- البريد الإلكتروني -->
							<div class="form-group mb-3">
								<label for="email" class="form-label">
									<i class="fa fa-envelope mr-2"></i>
									البريد الإلكتروني <span class="text-danger">*</span>
								</label>
								<input type="email"
									   class="form-control"
									   id="email"
									   name="email"
									   value="{{ form.email.value|default:'' }}"
									   required
									   placeholder="أدخل البريد الإلكتروني">
								<small class="form-text text-muted">
									سنستخدم هذا البريد لتأكيد حسابك وإرسال التحديثات
								</small>
								{% if form.email.errors %}
								<div class="text-danger small mt-1">
									{{ form.email.errors.0 }}
								</div>
								{% endif %}
							</div>

							<!-- رقم الهاتف -->
							<div class="form-group mb-3">
								<label for="phone_number" class="form-label">
									<i class="fa fa-phone mr-2"></i>
									رقم الهاتف <span class="text-danger">*</span>
								</label>
								<input type="tel"
									   class="form-control"
									   id="phone_number"
									   name="phone_number"
									   value="{{ form.phone_number.value|default:'' }}"
									   required
									   placeholder="مثال: 01234567890">
								{% if form.phone_number.errors %}
								<div class="text-danger small mt-1">
									{{ form.phone_number.errors.0 }}
								</div>
								{% endif %}
							</div>

							<!-- كلمة المرور -->
							<div class="row">
								<div class="col-md-6 mb-3">
									<label for="password" class="form-label">
										<i class="fa fa-lock mr-2"></i>
										كلمة المرور <span class="text-danger">*</span>
									</label>
									<div class="input-group">
										<input type="password"
											   class="form-control"
											   id="password"
											   name="password"
											   required
											   minlength="8"
											   placeholder="أدخل كلمة المرور">
										<div class="input-group-append">
											<button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
												<i class="fa fa-eye" id="password_icon"></i>
											</button>
										</div>
									</div>
									<small class="form-text text-muted">
										8 أحرف على الأقل
									</small>
									{% if form.password.errors %}
									<div class="text-danger small mt-1">
										{{ form.password.errors.0 }}
									</div>
									{% endif %}
								</div>
								<div class="col-md-6 mb-3">
									<label for="confirm_password" class="form-label">
										<i class="fa fa-check-circle mr-2"></i>
										تأكيد كلمة المرور <span class="text-danger">*</span>
									</label>
									<div class="input-group">
										<input type="password"
											   class="form-control"
											   id="confirm_password"
											   name="confirm_password"
											   required
											   minlength="8"
											   placeholder="أعد إدخال كلمة المرور">
										<div class="input-group-append">
											<button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
												<i class="fa fa-eye" id="confirm_password_icon"></i>
											</button>
										</div>
									</div>
									<small class="form-text text-muted">
										يجب أن تطابق كلمة المرور
									</small>
								</div>
							</div>

							<!-- مؤشر قوة كلمة المرور -->
							<div class="form-group mb-4">
								<label class="form-label">قوة كلمة المرور:</label>
								<div class="progress" style="height: 8px;">
									<div class="progress-bar" id="password-strength" role="progressbar" style="width: 0%"></div>
								</div>
								<small class="form-text" id="password-strength-text">أدخل كلمة المرور لرؤية قوتها</small>
							</div>

							<!-- شروط الاستخدام -->
							<div class="form-group mb-4">
								<div class="custom-control custom-checkbox">
									<input type="checkbox" class="custom-control-input" id="terms" name="terms" required>
									<label class="custom-control-label" for="terms">
										أوافق على <a href="#" class="text-primary">شروط الاستخدام</a> و <a href="#" class="text-primary">سياسة الخصوصية</a>
									</label>
								</div>
							</div>

							<!-- زر التسجيل -->
							<div class="form-group mb-3">
								<button type="submit" class="btn btn-primary btn-lg btn-block">
									<i class="fa fa-user-plus mr-2"></i>
									إنشاء الحساب
								</button>
							</div>

							<!-- رابط تسجيل الدخول -->
							<div class="text-center">
								<p class="mb-0">
									لديك حساب بالفعل؟
									<a href="{% url 'accounts:login' %}" class="text-primary font-weight-bold">
										سجل دخولك هنا
									</a>
								</p>
							</div>
						</form>
					</div>
				</div>

				<!-- معلومات إضافية -->
				<div class="card mt-4 border-0 bg-transparent">
					<div class="card-body text-center">
						<h6 class="text-muted mb-3">لماذا تنضم إلينا؟</h6>
						<div class="row">
							<div class="col-4">
								<i class="fa fa-shipping-fast fa-2x text-primary mb-2"></i>
								<p class="small text-muted">شحن سريع</p>
							</div>
							<div class="col-4">
								<i class="fa fa-shield-alt fa-2x text-success mb-2"></i>
								<p class="small text-muted">دفع آمن</p>
							</div>
							<div class="col-4">
								<i class="fa fa-headset fa-2x text-info mb-2"></i>
								<p class="small text-muted">دعم 24/7</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
<!-- ========================= نهاية محتوى التسجيل ========================= -->

<script>
// إظهار/إخفاء كلمة المرور
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');

    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// فحص قوة كلمة المرور
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.getElementById('password-strength');
    const strengthText = document.getElementById('password-strength-text');

    let strength = 0;
    let feedback = [];

    // طول كلمة المرور
    if (password.length >= 8) {
        strength += 20;
    } else {
        feedback.push('8 أحرف على الأقل');
    }

    // أحرف صغيرة
    if (/[a-z]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('أحرف صغيرة');
    }

    // أحرف كبيرة
    if (/[A-Z]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('أحرف كبيرة');
    }

    // أرقام
    if (/[0-9]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('أرقام');
    }

    // رموز خاصة
    if (/[^A-Za-z0-9]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('رموز خاصة');
    }

    // تحديث شريط القوة
    strengthBar.style.width = strength + '%';

    if (strength < 40) {
        strengthBar.className = 'progress-bar bg-danger';
        strengthText.textContent = 'ضعيفة - تحتاج: ' + feedback.join(', ');
        strengthText.className = 'form-text text-danger';
    } else if (strength < 80) {
        strengthBar.className = 'progress-bar bg-warning';
        strengthText.textContent = 'متوسطة - تحتاج: ' + feedback.join(', ');
        strengthText.className = 'form-text text-warning';
    } else {
        strengthBar.className = 'progress-bar bg-success';
        strengthText.textContent = 'قوية - ممتازة!';
        strengthText.className = 'form-text text-success';
    }
});

// التحقق من تطابق كلمات المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;

    if (confirmPassword && password !== confirmPassword) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
        if (confirmPassword) {
            this.classList.add('is-valid');
        }
    }
});
</script>

{% endblock %}